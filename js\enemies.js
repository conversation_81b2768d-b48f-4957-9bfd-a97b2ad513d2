// Enemy system for Space Shooter with diverse movement patterns

class Enemy {
    constructor(x, y, type = 'basic') {
        this.position = new Vector2(x, y);
        this.velocity = new Vector2(0, 0);
        this.type = type;
        this.size = new Vector2(24, 20);
        this.radius = 12;
        
        // Enemy stats
        this.health = 1;
        this.maxHealth = 1;
        this.isAlive = true;
        this.scoreValue = 100;
        
        // Movement properties
        this.speed = 2;
        this.movementTimer = 0;
        this.movementPattern = 'straight';
        
        // Combat properties
        this.canShoot = false;
        this.shootCooldown = 0;
        this.shootDelay = 2000;
        this.projectileSpeed = 4;
        
        // Visual properties
        this.color = COLORS.ENEMY;
        this.hitFlash = 0;
        this.thrusterParticles = [];
        
        // Initialize based on type
        this.initializeType();
        
        console.log(`Enemy created: ${type} at (${x}, ${y})`);
    }

    initializeType() {
        switch (this.type) {
            case 'basic':
                this.health = 1;
                this.speed = 2;
                this.scoreValue = 100;
                this.movementPattern = 'straight';
                break;
                
            case 'fast':
                this.health = 1;
                this.speed = 4;
                this.scoreValue = 150;
                this.movementPattern = 'diagonal';
                this.color = '#ff6600';
                break;
                
            case 'zigzag':
                this.health = 2;
                this.speed = 2.5;
                this.scoreValue = 200;
                this.movementPattern = 'zigzag';
                this.color = '#ff0066';
                break;
                
            case 'circular':
                this.health = 2;
                this.speed = 1.5;
                this.scoreValue = 250;
                this.movementPattern = 'circular';
                this.color = '#6600ff';
                break;
                
            case 'shooter':
                this.health = 3;
                this.speed = 1;
                this.scoreValue = 300;
                this.movementPattern = 'straight';
                this.canShoot = true;
                this.shootDelay = 1500;
                this.color = '#ff3300';
                break;
                
            case 'heavy':
                this.health = 5;
                this.speed = 1;
                this.scoreValue = 500;
                this.movementPattern = 'straight';
                this.canShoot = true;
                this.shootDelay = 1000;
                this.size = new Vector2(32, 28);
                this.radius = 16;
                this.color = '#990000';
                break;
        }
        
        this.maxHealth = this.health;
    }

    update(deltaTime) {
        if (!this.isAlive) return;
        
        // Update timers
        this.updateTimers(deltaTime);
        
        // Update movement
        this.updateMovement(deltaTime);
        
        // Update shooting
        this.updateShooting(deltaTime);
        
        // Update visual effects
        this.updateEffects(deltaTime);
    }

    updateTimers(deltaTime) {
        this.movementTimer += deltaTime;
        
        if (this.shootCooldown > 0) {
            this.shootCooldown -= deltaTime;
        }
        
        if (this.hitFlash > 0) {
            this.hitFlash -= deltaTime * 0.01;
        }
    }

    updateMovement(deltaTime) {
        switch (this.movementPattern) {
            case 'straight':
                this.moveStraight(deltaTime);
                break;
            case 'diagonal':
                this.moveDiagonal(deltaTime);
                break;
            case 'zigzag':
                this.moveZigzag(deltaTime);
                break;
            case 'circular':
                this.moveCircular(deltaTime);
                break;
            case 'formation':
                this.moveFormation(deltaTime);
                break;
        }
        
        // Apply velocity to position
        this.position.x += this.velocity.x * deltaTime * 0.1;
        this.position.y += this.velocity.y * deltaTime * 0.1;
        
        // Create thruster particles
        if (this.velocity.magnitude() > 0.5) {
            this.createThrusterParticles();
        }
    }

    moveStraight(deltaTime) {
        this.velocity.x = -this.speed;
        this.velocity.y = 0;
    }

    moveDiagonal(deltaTime) {
        // Move diagonally towards player
        if (window.gameEngine && window.gameEngine.player) {
            const player = window.gameEngine.player;
            const direction = player.position.subtract(this.position).normalize();
            this.velocity.x = direction.x * this.speed * 0.7 - this.speed * 0.3;
            this.velocity.y = direction.y * this.speed * 0.5;
        } else {
            this.velocity.x = -this.speed;
            this.velocity.y = Math.sin(this.movementTimer * 0.003) * this.speed * 0.5;
        }
    }

    moveZigzag(deltaTime) {
        this.velocity.x = -this.speed;
        this.velocity.y = Math.sin(this.movementTimer * 0.008) * this.speed * 2;
    }

    moveCircular(deltaTime) {
        const centerX = this.position.x;
        const centerY = GAME_CONFIG.CANVAS_HEIGHT / 2;
        const radius = 80;
        const angularSpeed = 0.003;
        
        const angle = this.movementTimer * angularSpeed;
        const targetX = centerX + Math.cos(angle) * radius - this.speed;
        const targetY = centerY + Math.sin(angle) * radius;
        
        this.velocity.x = (targetX - this.position.x) * 0.1;
        this.velocity.y = (targetY - this.position.y) * 0.1;
    }

    moveFormation(deltaTime) {
        // Formation movement - maintain relative position to formation leader
        if (this.formationData) {
            const targetX = this.formationData.leaderX + this.formationData.offsetX;
            const targetY = this.formationData.leaderY + this.formationData.offsetY;
            
            this.velocity.x = (targetX - this.position.x) * 0.05;
            this.velocity.y = (targetY - this.position.y) * 0.05;
        } else {
            this.moveStraight(deltaTime);
        }
    }

    updateShooting(deltaTime) {
        if (!this.canShoot || this.shootCooldown > 0) return;
        
        // Check if player is in range and roughly in front
        if (window.gameEngine && window.gameEngine.player) {
            const player = window.gameEngine.player;
            const distance = this.position.distance(player.position);
            
            if (distance < 400 && player.position.x < this.position.x) {
                this.shoot();
                this.shootCooldown = this.shootDelay + Utils.random(-200, 200);
            }
        }
    }

    shoot() {
        const startX = this.position.x - 10;
        const startY = this.position.y + this.size.y / 2;
        
        // Aim towards player
        let targetX = startX - 100;
        let targetY = startY;
        
        if (window.gameEngine && window.gameEngine.player) {
            const player = window.gameEngine.player;
            targetX = player.position.x;
            targetY = player.position.y;
        }
        
        const direction = new Vector2(targetX - startX, targetY - startY).normalize();
        const projectile = new EnemyProjectile(
            startX, startY,
            direction.x * this.projectileSpeed,
            direction.y * this.projectileSpeed,
            1, // damage
            this.type === 'heavy' ? 'sine' : 'straight'
        );
        
        if (window.gameEngine) {
            window.gameEngine.projectiles.push(projectile);
        }
    }

    createThrusterParticles() {
        const particleX = this.position.x + this.size.x / 2;
        const particleY = this.position.y + this.size.y / 2 + Utils.random(-3, 3);
        
        this.thrusterParticles.push({
            x: particleX,
            y: particleY,
            vx: Utils.random(1, 2),
            vy: Utils.random(-0.5, 0.5),
            life: 1.0,
            decay: 0.06,
            size: Utils.random(1, 2),
            color: Utils.randomChoice(['#ff4400', '#ff6600', '#ff8800'])
        });
        
        // Limit particle count
        if (this.thrusterParticles.length > 8) {
            this.thrusterParticles.shift();
        }
    }

    updateEffects(deltaTime) {
        // Update thruster particles
        this.thrusterParticles.forEach(particle => {
            particle.x += particle.vx * deltaTime * 0.1;
            particle.y += particle.vy * deltaTime * 0.1;
            particle.life -= particle.decay;
        });
        
        this.thrusterParticles = this.thrusterParticles.filter(particle => particle.life > 0);
    }

    takeDamage(damage = 1) {
        if (!this.isAlive) return;
        
        this.health -= damage;
        this.hitFlash = 1.0;
        
        // Create hit sparks
        if (window.particleSystem) {
            window.particleSystem.createSparks(
                this.position.x + this.size.x / 2,
                this.position.y + this.size.y / 2,
                Math.PI, // Direction towards left
                { count: 3, speed: 2 }
            );
        }
        
        if (this.health <= 0) {
            this.destroy();
        }
    }

    destroy() {
        this.isAlive = false;

        // Create explosion using the game engine's explosion system
        if (window.gameEngine) {
            window.gameEngine.addExplosion(
                this.position.x + this.size.x / 2,
                this.position.y + this.size.y / 2,
                this.type === 'heavy' ? 'large' : 'normal'
            );
        }
    }

    render(ctx) {
        if (!this.isAlive) return;
        
        // Render thruster particles first
        this.renderThrusterParticles(ctx);
        
        // Apply hit flash effect
        if (this.hitFlash > 0) {
            ctx.fillStyle = `rgba(255, 255, 255, ${this.hitFlash})`;
            ctx.fillRect(this.position.x - 2, this.position.y - 2, this.size.x + 4, this.size.y + 4);
        }
        
        // Render enemy ship
        this.renderShip(ctx);
        
        // Render health bar for tougher enemies
        if (this.maxHealth > 1) {
            this.renderHealthBar(ctx);
        }
    }

    renderThrusterParticles(ctx) {
        this.thrusterParticles.forEach(particle => {
            ctx.fillStyle = particle.color;
            ctx.globalAlpha = particle.life;
            ctx.fillRect(particle.x, particle.y, particle.size, particle.size);
        });
        ctx.globalAlpha = 1;
    }

    renderShip(ctx) {
        const x = Math.floor(this.position.x);
        const y = Math.floor(this.position.y);
        
        // Ship body
        ctx.fillStyle = this.color;
        ctx.fillRect(x, y + 6, this.size.x - 8, 8);
        ctx.fillRect(x + 4, y + 2, this.size.x - 12, 16);
        ctx.fillRect(x + 8, y, this.size.x - 16, this.size.y);
        
        // Ship details
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(x + 2, y + 8, 2, 4);
        ctx.fillRect(x + 6, y + 6, 2, 8);
        
        // Weapon indicators for shooters
        if (this.canShoot) {
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(x, y + 9, 2, 2);
            ctx.fillRect(x, y + 11, 2, 2);
        }
        
        // Special markings for different types
        switch (this.type) {
            case 'fast':
                ctx.fillStyle = '#ffff00';
                ctx.fillRect(x + this.size.x - 4, y + 8, 2, 4);
                break;
            case 'heavy':
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(x + this.size.x - 6, y + 6, 4, 8);
                break;
        }
    }

    renderHealthBar(ctx) {
        if (this.health >= this.maxHealth) return;
        
        const barWidth = this.size.x;
        const barHeight = 3;
        const x = this.position.x;
        const y = this.position.y - 8;
        
        // Background
        ctx.fillStyle = '#333333';
        ctx.fillRect(x, y, barWidth, barHeight);
        
        // Health
        const healthPercent = this.health / this.maxHealth;
        const healthColor = healthPercent > 0.5 ? '#00ff00' : healthPercent > 0.25 ? '#ffff00' : '#ff0000';
        ctx.fillStyle = healthColor;
        ctx.fillRect(x, y, barWidth * healthPercent, barHeight);
        
        // Border
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 1;
        ctx.strokeRect(x, y, barWidth, barHeight);
    }
}

// Formation enemy class
class FormationEnemy extends Enemy {
    constructor(x, y, formationData) {
        super(x, y, 'formation');
        this.formationData = formationData;
        this.movementPattern = 'formation';
        this.health = 1;
        this.scoreValue = 150;
        this.color = '#00ff88';
    }
}

// Enemy spawner and manager
class EnemyManager {
    constructor() {
        this.enemies = [];
        this.spawnTimer = 0;
        this.spawnDelay = 2000;
        this.currentWave = 1;
        this.enemiesSpawned = 0;
        this.maxEnemiesPerWave = 10;
        
        // Formation spawning
        this.formationTimer = 0;
        this.formationDelay = 8000;
    }

    update(deltaTime) {
        this.spawnTimer += deltaTime;
        this.formationTimer += deltaTime;
        
        // Spawn regular enemies
        if (this.spawnTimer >= this.spawnDelay && this.enemiesSpawned < this.maxEnemiesPerWave) {
            this.spawnRandomEnemy();
            this.spawnTimer = 0;
            this.enemiesSpawned++;
        }
        
        // Spawn formations
        if (this.formationTimer >= this.formationDelay) {
            this.spawnFormation();
            this.formationTimer = 0;
        }
        
        // Check for wave completion
        if (this.enemiesSpawned >= this.maxEnemiesPerWave && this.enemies.length === 0) {
            this.nextWave();
        }
    }

    spawnRandomEnemy() {
        const types = ['basic', 'fast', 'zigzag', 'circular', 'shooter'];
        const weights = [40, 25, 15, 10, 10]; // Probability weights
        
        // Adjust weights based on current wave
        if (this.currentWave > 2) {
            weights[4] += 10; // More shooters
        }
        if (this.currentWave > 3) {
            types.push('heavy');
            weights.push(5);
        }
        
        const type = this.weightedRandomChoice(types, weights);
        const x = GAME_CONFIG.CANVAS_WIDTH + 50;
        const y = Utils.random(50, GAME_CONFIG.CANVAS_HEIGHT - 50);
        
        const enemy = new Enemy(x, y, type);
        this.enemies.push(enemy);
        
        return enemy;
    }

    spawnFormation() {
        const formationSize = Utils.randomInt(3, 6);
        const leaderX = GAME_CONFIG.CANVAS_WIDTH + 100;
        const leaderY = Utils.random(100, GAME_CONFIG.CANVAS_HEIGHT - 100);
        
        for (let i = 0; i < formationSize; i++) {
            const offsetX = (i % 2) * 40 - 20;
            const offsetY = Math.floor(i / 2) * 30 - 30;
            
            const formationData = {
                leaderX: leaderX,
                leaderY: leaderY,
                offsetX: offsetX,
                offsetY: offsetY
            };
            
            const enemy = new FormationEnemy(
                leaderX + offsetX,
                leaderY + offsetY,
                formationData
            );
            
            this.enemies.push(enemy);
        }
    }

    weightedRandomChoice(items, weights) {
        const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
        let random = Math.random() * totalWeight;
        
        for (let i = 0; i < items.length; i++) {
            random -= weights[i];
            if (random <= 0) {
                return items[i];
            }
        }
        
        return items[0];
    }

    nextWave() {
        this.currentWave++;
        this.enemiesSpawned = 0;
        this.maxEnemiesPerWave += 2;
        this.spawnDelay = Math.max(1000, this.spawnDelay - 100);
        
        console.log(`Wave ${this.currentWave} starting`);
    }

    clear() {
        this.enemies = [];
        this.enemiesSpawned = 0;
    }
}
