// Core Game Engine for Space Shooter

class GameEngine {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.lastTime = 0;
        this.deltaTime = 0;
        this.isRunning = false;
        this.isPaused = false;
        
        // Game state
        this.gameState = 'menu'; // menu, playing, paused, gameOver, stageComplete
        this.score = 0;
        this.lives = 3;
        this.currentStage = 1;
        this.stageProgress = 0;

        // Combo system
        this.combo = 0;
        this.comboTimer = 0;
        this.comboTimeout = 3000; // 3 seconds to maintain combo
        this.maxCombo = 0;
        
        // Game objects
        this.player = null;
        this.enemies = [];
        this.projectiles = [];
        this.powerUps = [];
        this.particles = [];
        this.bosses = [];
        
        // Background layers for parallax scrolling
        this.backgroundLayers = [];
        this.backgroundOffset = 0;
        
        // Stage management
        this.stageManager = null;
        this.enemySpawnTimer = 0;
        this.stageTimer = 0;
        
        // Performance tracking
        this.frameCount = 0;
        this.fps = 0;
        this.fpsTimer = 0;
        
        this.init();
    }

    init() {
        // Set up canvas
        this.canvas.width = GAME_CONFIG.CANVAS_WIDTH;
        this.canvas.height = GAME_CONFIG.CANVAS_HEIGHT;
        
        // Initialize background layers
        this.initBackground();
        
        // Set up event listeners
        this.setupEventListeners();
        
        console.log('Game Engine initialized');
    }

    initBackground() {
        // Create multiple background layers for parallax effect
        this.backgroundLayers = [
            {
                stars: this.generateStars(50, 1, '#ffffff'),
                speed: 0.5,
                offset: 0
            },
            {
                stars: this.generateStars(30, 2, '#aaaaff'),
                speed: 1,
                offset: 0
            },
            {
                stars: this.generateStars(20, 3, '#ffaaaa'),
                speed: 1.5,
                offset: 0
            }
        ];
    }

    generateStars(count, size, color) {
        const stars = [];
        for (let i = 0; i < count; i++) {
            stars.push({
                x: Math.random() * (GAME_CONFIG.CANVAS_WIDTH + 100),
                y: Math.random() * GAME_CONFIG.CANVAS_HEIGHT,
                size: size,
                color: color,
                brightness: Math.random() * 0.5 + 0.5
            });
        }
        return stars;
    }

    setupEventListeners() {
        // Game control buttons
        document.getElementById('startButton').addEventListener('click', () => this.startGame());
        document.getElementById('resumeButton').addEventListener('click', () => this.resumeGame());
        document.getElementById('restartButton').addEventListener('click', () => this.restartGame());
        document.getElementById('playAgainButton').addEventListener('click', () => this.restartGame());
        document.getElementById('nextStageButton').addEventListener('click', () => this.nextStage());
        
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // Prevent context menu on canvas
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    handleKeyDown(event) {
        if (event.code === 'Escape') {
            if (this.gameState === 'playing') {
                this.pauseGame();
            } else if (this.gameState === 'paused') {
                this.resumeGame();
            }
        }
        
        // Pass to input handler
        if (window.inputHandler) {
            window.inputHandler.handleKeyDown(event);
        }
    }

    handleKeyUp(event) {
        // Pass to input handler
        if (window.inputHandler) {
            window.inputHandler.handleKeyUp(event);
        }
    }

    startGame() {
        this.gameState = 'playing';
        this.score = 0;
        this.lives = 3;
        this.currentStage = 1;
        this.stageProgress = 0;
        
        // Clear all game objects
        this.enemies = [];
        this.projectiles = [];
        this.powerUps = [];
        this.particles = [];
        this.bosses = [];
        
        // Initialize player
        this.player = new Player(100, GAME_CONFIG.CANVAS_HEIGHT / 2);
        
        // Initialize stage manager
        this.stageManager = new StageManager(this);
        this.stageManager.loadStage(this.currentStage);
        
        // Hide menu and show game
        this.hideAllScreens();
        this.updateUI();
        
        // Start game loop
        this.isRunning = true;
        this.gameLoop();
        
        console.log('Game started');
    }

    pauseGame() {
        this.gameState = 'paused';
        this.showScreen('pauseScreen');
    }

    resumeGame() {
        this.gameState = 'playing';
        this.hideAllScreens();
    }

    restartGame() {
        this.hideAllScreens();
        this.startGame();
    }

    nextStage() {
        this.currentStage++;
        this.stageProgress = 0;
        this.stageManager.loadStage(this.currentStage);
        this.gameState = 'playing';
        this.hideAllScreens();
        this.updateUI();
    }

    gameOver() {
        this.gameState = 'gameOver';
        this.showScreen('gameOverScreen');
        document.getElementById('finalScoreValue').textContent = Utils.formatScore(this.score);
        
        // Stop background music
        if (window.audioManager) {
            window.audioManager.stopMusic();
        }
    }

    stageComplete() {
        this.gameState = 'stageComplete';
        const stageBonus = this.currentStage * 1000;
        this.score += stageBonus;
        
        this.showScreen('stageCompleteScreen');
        document.getElementById('stageBonusValue').textContent = Utils.formatScore(stageBonus, 4);
        this.updateUI();
    }

    showScreen(screenId) {
        this.hideAllScreens();
        document.getElementById(screenId).classList.remove('hidden');
    }

    hideAllScreens() {
        const screens = ['startScreen', 'pauseScreen', 'gameOverScreen', 'stageCompleteScreen'];
        screens.forEach(screenId => {
            document.getElementById(screenId).classList.add('hidden');
        });
    }

    updateUI() {
        document.getElementById('score').textContent = Utils.formatScore(this.score);
        document.getElementById('currentStage').textContent = this.currentStage;

        // Update lives display
        const livesContainer = document.getElementById('livesContainer');
        const lifeElements = livesContainer.querySelectorAll('.life');
        lifeElements.forEach((life, index) => {
            if (index < this.lives) {
                life.classList.remove('lost');
            } else {
                life.classList.add('lost');
            }
        });

        // Update weapon display
        if (this.player && this.player.currentWeapon) {
            document.getElementById('currentWeapon').textContent = this.player.currentWeapon.name;
        }

        // Update combo display
        const comboElement = document.getElementById('currentCombo');
        if (comboElement) {
            comboElement.textContent = this.combo > 0 ? this.combo.toString() : '0';
            if (this.combo > 0) {
                comboElement.style.color = this.combo >= 10 ? '#ff0080' : '#ffff00';
                comboElement.style.textShadow = `0 0 8px ${this.combo >= 10 ? '#ff0080' : '#ffff00'}`;
            }
        }
    }

    gameLoop(currentTime = 0) {
        if (!this.isRunning) return;
        
        // Calculate delta time
        this.deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // Update FPS counter
        this.updateFPS();
        
        // Only update game logic if not paused
        if (this.gameState === 'playing') {
            this.update();
        }
        
        this.render();
        
        // Continue game loop
        requestAnimationFrame((time) => this.gameLoop(time));
    }

    updateFPS() {
        this.frameCount++;
        this.fpsTimer += this.deltaTime;
        
        if (this.fpsTimer >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.fpsTimer = 0;
        }
    }

    update() {
        // Update stage timer
        this.stageTimer += this.deltaTime;

        // Update combo timer
        this.updateCombo(this.deltaTime);

        // Update background
        this.updateBackground();
        
        // Update player
        if (this.player) {
            this.player.update(this.deltaTime);
        }
        
        // Update enemies
        this.enemies.forEach(enemy => enemy.update(this.deltaTime));
        this.enemies = this.enemies.filter(enemy => enemy.isAlive && Utils.isInBounds(enemy.position, 100));
        
        // Update projectiles
        this.projectiles.forEach(projectile => projectile.update(this.deltaTime));
        this.projectiles = this.projectiles.filter(projectile => 
            projectile.isAlive && Utils.isInBounds(projectile.position, 50));
        
        // Update power-ups
        this.powerUps.forEach(powerUp => powerUp.update(this.deltaTime));
        this.powerUps = this.powerUps.filter(powerUp => 
            powerUp.isAlive && Utils.isInBounds(powerUp.position, 50));
        
        // Update particles
        this.updateSimpleParticles();
        this.particles = this.particles.filter(particle => particle.life > 0);

        // Update particle system
        if (window.particleSystem) {
            window.particleSystem.update(this.deltaTime);
        }
        
        // Update bosses
        this.bosses.forEach(boss => boss.update(this.deltaTime));
        this.bosses = this.bosses.filter(boss => boss.isAlive);
        
        // Update stage manager
        if (this.stageManager) {
            this.stageManager.update(this.deltaTime);
        }
        
        // Check collisions
        this.checkCollisions();
        
        // Check game state
        this.checkGameState();
    }

    updateCombo(deltaTime) {
        if (this.combo > 0) {
            this.comboTimer -= deltaTime;
            if (this.comboTimer <= 0) {
                this.combo = 0;
            }
        }
    }

    updateSimpleParticles() {
        this.particles.forEach(particle => {
            // Update position
            particle.x += particle.vx * this.deltaTime * 0.1;
            particle.y += particle.vy * this.deltaTime * 0.1;

            // Update life
            particle.life -= particle.decay;
        });
    }

    updateBackground() {
        this.backgroundLayers.forEach(layer => {
            layer.offset += layer.speed * this.deltaTime * 0.1;

            // Reset stars that have moved off screen
            layer.stars.forEach(star => {
                star.x -= layer.speed * this.deltaTime * 0.1;
                if (star.x < -10) {
                    star.x = GAME_CONFIG.CANVAS_WIDTH + 10;
                    star.y = Math.random() * GAME_CONFIG.CANVAS_HEIGHT;
                }
            });
        });
    }

    checkCollisions() {
        // Player vs enemies
        if (this.player && this.player.isAlive) {
            this.enemies.forEach(enemy => {
                if (enemy.isAlive && Utils.circleCollision(this.player, enemy)) {
                    this.player.takeDamage();
                    enemy.destroy();
                    this.addExplosion(enemy.position.x, enemy.position.y);
                }
            });
        }

        // Player bullets vs enemies
        this.projectiles.forEach(projectile => {
            if (projectile.type === 'player') {
                this.enemies.forEach(enemy => {
                    if (enemy.isAlive && Utils.circleCollision(projectile, enemy)) {
                        enemy.takeDamage(projectile.damage);
                        projectile.destroy();

                        if (!enemy.isAlive) {
                            // Add score with combo multiplier
                            const baseScore = enemy.scoreValue || 100; // Default score if undefined
                            const comboMultiplier = Math.min(1 + (this.combo * 0.1), 3); // Max 3x multiplier
                            const finalScore = Math.floor(baseScore * comboMultiplier);
                            this.score += finalScore;

                            // Update combo
                            this.combo++;
                            this.comboTimer = this.comboTimeout;
                            this.maxCombo = Math.max(this.maxCombo, this.combo);

                            this.addExplosion(enemy.position.x, enemy.position.y);

                            // Chance to drop power-up
                            if (Math.random() < GAME_CONFIG.POWERUP_DROP_CHANCE) {
                                this.addPowerUp(enemy.position.x, enemy.position.y);
                            }
                        }
                    }
                });

                // Player bullets vs bosses
                this.bosses.forEach(boss => {
                    if (boss.isAlive && Utils.circleCollision(projectile, boss)) {
                        boss.takeDamage(projectile.damage, projectile.position);
                        projectile.destroy();

                        if (!boss.isAlive) {
                            this.score += boss.scoreValue || 5000; // Default boss score if undefined
                            this.addExplosion(boss.position.x, boss.position.y, 'large');
                        }
                    }
                });
            }
        });

        // Enemy bullets vs player
        if (this.player && this.player.isAlive) {
            this.projectiles.forEach(projectile => {
                if (projectile.type === 'enemy' && Utils.circleCollision(this.player, projectile)) {
                    this.player.takeDamage();
                    projectile.destroy();
                }
            });
        }

        // Player vs power-ups
        if (this.player && this.player.isAlive) {
            this.powerUps.forEach(powerUp => {
                if (powerUp.isAlive && Utils.circleCollision(this.player, powerUp)) {
                    if (window.powerUpManager) {
                        window.powerUpManager.collectPowerUp(powerUp, this.player);
                    } else {
                        this.player.collectPowerUp(powerUp);
                        powerUp.destroy();
                    }
                    this.score += 100;
                }
            });
        }
    }

    checkGameState() {
        // Check if player is dead
        if (this.player && !this.player.isAlive) {
            this.lives--;
            this.updateUI();
            
            if (this.lives <= 0) {
                this.gameOver();
            } else {
                // Respawn player
                this.player = new Player(100, GAME_CONFIG.CANVAS_HEIGHT / 2);
            }
        }
        
        // Check stage completion
        if (this.stageManager && this.stageManager.isStageComplete()) {
            if (this.currentStage >= 5) {
                // Game complete
                this.gameOver();
            } else {
                this.stageComplete();
            }
        }
    }

    addExplosion(x, y, size = 'normal') {
        const particleCount = size === 'large' ? 20 : 10;
        const explosionParticles = Utils.createExplosion(x, y, particleCount);
        this.particles.push(...explosionParticles);
        
        // Play explosion sound
        if (window.audioManager) {
            window.audioManager.playSound('explosion');
        }
    }

    addPowerUp(x, y) {
        const powerUpTypes = ['spreadShot', 'rapidFire', 'laser', 'homingMissile', 'shield'];
        const type = Utils.randomChoice(powerUpTypes);
        this.powerUps.push(new PowerUp(x, y, type));
    }

    render() {
        // Clear canvas
        this.ctx.fillStyle = COLORS.BACKGROUND;
        this.ctx.fillRect(0, 0, GAME_CONFIG.CANVAS_WIDTH, GAME_CONFIG.CANVAS_HEIGHT);
        
        // Render background
        this.renderBackground();
        
        // Render game objects
        this.renderSimpleParticles();

        // Render particle system
        if (window.particleSystem) {
            window.particleSystem.render(this.ctx);
        }

        this.powerUps.forEach(powerUp => powerUp.render(this.ctx));
        this.projectiles.forEach(projectile => projectile.render(this.ctx));
        this.enemies.forEach(enemy => enemy.render(this.ctx));
        this.bosses.forEach(boss => boss.render(this.ctx));
        
        if (this.player) {
            this.player.render(this.ctx);
        }
        
        // Render debug info in development
        if (window.DEBUG) {
            this.renderDebugInfo();
        }
    }

    renderBackground() {
        this.backgroundLayers.forEach(layer => {
            layer.stars.forEach(star => {
                this.ctx.fillStyle = star.color;
                this.ctx.globalAlpha = star.brightness;
                this.ctx.fillRect(star.x, star.y, star.size, star.size);
            });
        });
        this.ctx.globalAlpha = 1;
    }

    renderSimpleParticles() {
        this.particles.forEach(particle => {
            if (particle.life > 0) {
                this.ctx.fillStyle = particle.color;
                this.ctx.globalAlpha = particle.life;
                this.ctx.fillRect(
                    particle.x - particle.size / 2,
                    particle.y - particle.size / 2,
                    particle.size,
                    particle.size
                );
            }
        });
        this.ctx.globalAlpha = 1;
    }

    renderDebugInfo() {
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '12px monospace';
        this.ctx.fillText(`FPS: ${this.fps}`, 10, 20);
        this.ctx.fillText(`Enemies: ${this.enemies.length}`, 10, 35);
        this.ctx.fillText(`Projectiles: ${this.projectiles.length}`, 10, 50);
        this.ctx.fillText(`Particles: ${this.particles.length}`, 10, 65);
    }
}

// Initialize game engine when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.gameEngine = new GameEngine();
});
