// Player ship implementation for Space Shooter

class Player {
    constructor(x, y) {
        this.position = new Vector2(x, y);
        this.velocity = new Vector2(0, 0);
        this.size = new Vector2(32, 24);
        this.radius = 12; // For collision detection
        
        // Player stats
        this.health = 1;
        this.maxHealth = 1;
        this.isAlive = true;
        this.isInvulnerable = false;
        this.invulnerabilityTime = 0;
        this.invulnerabilityDuration = 2000; // 2 seconds
        
        // Movement
        this.speed = GAME_CONFIG.PLAYER_SPEED;
        this.acceleration = 0.8;
        this.friction = 0.85;
        
        // Shooting
        this.canShoot = true;
        this.shootCooldown = 0;
        this.shootDelay = 150; // milliseconds between shots
        
        // Weapons system
        this.currentWeapon = {
            name: 'BASIC',
            type: 'basic',
            damage: 1,
            projectileSpeed: 8,
            spread: 0,
            projectileCount: 1,
            duration: Infinity
        };
        
        this.weaponTimer = 0;
        
        // Visual effects
        this.thrusterParticles = [];
        this.hitFlash = 0;
        this.engineGlow = 0;
        
        // Input state
        this.input = {
            left: false,
            right: false,
            up: false,
            down: false,
            shoot: false
        };
        
        console.log('Player created at', x, y);
    }

    update(deltaTime) {
        if (!this.isAlive) return;
        
        // Update timers
        this.updateTimers(deltaTime);
        
        // Handle input
        this.handleInput();
        
        // Update movement
        this.updateMovement(deltaTime);
        
        // Update shooting
        this.updateShooting(deltaTime);
        
        // Update visual effects
        this.updateEffects(deltaTime);
        
        // Keep player in bounds
        this.constrainToBounds();
    }

    updateTimers(deltaTime) {
        // Invulnerability timer
        if (this.isInvulnerable) {
            this.invulnerabilityTime -= deltaTime;
            if (this.invulnerabilityTime <= 0) {
                this.isInvulnerable = false;
            }
        }
        
        // Shoot cooldown
        if (this.shootCooldown > 0) {
            this.shootCooldown -= deltaTime;
        }
        
        // Weapon duration timer
        if (this.currentWeapon.duration !== Infinity) {
            this.weaponTimer += deltaTime;
            if (this.weaponTimer >= this.currentWeapon.duration) {
                this.resetToBasicWeapon();
            }
        }
        
        // Visual effect timers
        if (this.hitFlash > 0) {
            this.hitFlash -= deltaTime * 0.01;
        }
    }

    handleInput() {
        // Get input state from input handler
        if (window.inputHandler) {
            this.input = window.inputHandler.getPlayerInput();
        }
    }

    updateMovement(deltaTime) {
        // Calculate target velocity based on input
        const targetVelocity = new Vector2(0, 0);
        
        if (this.input.left) targetVelocity.x -= this.speed;
        if (this.input.right) targetVelocity.x += this.speed;
        if (this.input.up) targetVelocity.y -= this.speed;
        if (this.input.down) targetVelocity.y += this.speed;
        
        // Normalize diagonal movement
        if (targetVelocity.magnitude() > this.speed) {
            targetVelocity = targetVelocity.normalize().multiply(this.speed);
        }
        
        // Apply acceleration/deceleration
        this.velocity.x = Utils.lerp(this.velocity.x, targetVelocity.x, this.acceleration);
        this.velocity.y = Utils.lerp(this.velocity.y, targetVelocity.y, this.acceleration);
        
        // Apply friction when no input
        if (targetVelocity.magnitude() === 0) {
            this.velocity.x *= this.friction;
            this.velocity.y *= this.friction;
        }
        
        // Update position
        this.position.x += this.velocity.x * deltaTime * 0.1;
        this.position.y += this.velocity.y * deltaTime * 0.1;
        
        // Update engine glow based on movement
        const movementIntensity = this.velocity.magnitude() / this.speed;
        this.engineGlow = Utils.lerp(this.engineGlow, movementIntensity, 0.1);
        
        // Create thruster particles when moving
        if (movementIntensity > 0.1) {
            this.createThrusterParticles();
        }
    }

    updateShooting(deltaTime) {
        if (this.input.shoot && this.canShoot && this.shootCooldown <= 0) {
            this.shoot();
            this.shootCooldown = this.shootDelay;
        }
    }

    shoot() {
        const weapon = this.currentWeapon;
        const startX = this.position.x + this.size.x;
        const startY = this.position.y + this.size.y / 2;
        
        switch (weapon.type) {
            case 'basic':
                this.createProjectile(startX, startY, weapon.projectileSpeed, 0, weapon.damage);
                break;
                
            case 'spreadShot':
                const angles = [-0.3, -0.15, 0, 0.15, 0.3];
                angles.forEach(angle => {
                    this.createProjectile(startX, startY, weapon.projectileSpeed, angle, weapon.damage);
                });
                break;
                
            case 'rapidFire':
                this.createProjectile(startX, startY, weapon.projectileSpeed, 0, weapon.damage);
                break;
                
            case 'laser':
                this.createLaserBeam(startX, startY);
                break;
                
            case 'homingMissile':
                this.createHomingMissile(startX, startY);
                break;
        }
        
        // Play shoot sound
        if (window.audioManager) {
            window.audioManager.playSound('shoot');
        }
    }

    createProjectile(x, y, speed, angle, damage) {
        const projectile = new Projectile(
            x, y,
            Math.cos(angle) * speed,
            Math.sin(angle) * speed,
            'player',
            damage
        );
        
        if (window.gameEngine) {
            window.gameEngine.projectiles.push(projectile);
        }
    }

    createLaserBeam(x, y) {
        // Create a continuous laser beam
        const laser = new LaserBeam(x, y, 'player', this.currentWeapon.damage);
        if (window.gameEngine) {
            window.gameEngine.projectiles.push(laser);
        }
    }

    createHomingMissile(x, y) {
        const missile = new HomingMissile(x, y, 'player', this.currentWeapon.damage);
        if (window.gameEngine) {
            window.gameEngine.projectiles.push(missile);
        }
    }

    createThrusterParticles() {
        // Create particles behind the ship
        const particleX = this.position.x - 5;
        const particleY = this.position.y + this.size.y / 2 + Utils.random(-3, 3);
        
        this.thrusterParticles.push({
            x: particleX,
            y: particleY,
            vx: Utils.random(-2, -1),
            vy: Utils.random(-0.5, 0.5),
            life: 1.0,
            decay: 0.05,
            size: Utils.random(1, 3),
            color: Utils.randomChoice(['#00aaff', '#0088ff', '#ffffff'])
        });
        
        // Limit particle count
        if (this.thrusterParticles.length > 20) {
            this.thrusterParticles.shift();
        }
    }

    updateEffects(deltaTime) {
        // Update thruster particles
        this.thrusterParticles.forEach(particle => {
            particle.x += particle.vx * deltaTime * 0.1;
            particle.y += particle.vy * deltaTime * 0.1;
            particle.life -= particle.decay;
        });
        
        // Remove dead particles
        this.thrusterParticles = this.thrusterParticles.filter(particle => particle.life > 0);
    }

    constrainToBounds() {
        const margin = 10;
        this.position.x = Utils.clamp(this.position.x, margin, GAME_CONFIG.CANVAS_WIDTH - this.size.x - margin);
        this.position.y = Utils.clamp(this.position.y, margin, GAME_CONFIG.CANVAS_HEIGHT - this.size.y - margin);
    }

    takeDamage(damage = 1) {
        if (this.isInvulnerable || !this.isAlive) return;
        
        this.health -= damage;
        this.hitFlash = 1.0;
        
        if (this.health <= 0) {
            this.destroy();
        } else {
            // Become temporarily invulnerable
            this.isInvulnerable = true;
            this.invulnerabilityTime = this.invulnerabilityDuration;
        }
        
        // Play hit sound
        if (window.audioManager) {
            window.audioManager.playSound('playerHit');
        }
    }

    destroy() {
        this.isAlive = false;
        
        // Create explosion effect
        if (window.gameEngine) {
            window.gameEngine.addExplosion(
                this.position.x + this.size.x / 2,
                this.position.y + this.size.y / 2,
                'large'
            );
        }
    }

    collectPowerUp(powerUp) {
        switch (powerUp.type) {
            case 'spreadShot':
                this.setWeapon('spreadShot', 'SPREAD', 1, 8, 15000);
                break;
            case 'rapidFire':
                this.setWeapon('rapidFire', 'RAPID', 1, 8, 10000);
                this.shootDelay = 75; // Faster shooting
                break;
            case 'laser':
                this.setWeapon('laser', 'LASER', 2, 10, 8000);
                break;
            case 'homingMissile':
                this.setWeapon('homingMissile', 'HOMING', 3, 6, 12000);
                break;
            case 'shield':
                this.activateShield();
                break;
        }
        
        // Play power-up sound
        if (window.audioManager) {
            window.audioManager.playSound('powerUp');
        }
    }

    setWeapon(type, name, damage, speed, duration) {
        this.currentWeapon = {
            name: name,
            type: type,
            damage: damage,
            projectileSpeed: speed,
            duration: duration
        };
        this.weaponTimer = 0;
    }

    activateShield() {
        this.isInvulnerable = true;
        this.invulnerabilityTime = 5000; // 5 seconds of invulnerability
    }

    resetToBasicWeapon() {
        this.currentWeapon = {
            name: 'BASIC',
            type: 'basic',
            damage: 1,
            projectileSpeed: 8,
            duration: Infinity
        };
        this.shootDelay = 150;
        this.weaponTimer = 0;
    }

    render(ctx) {
        if (!this.isAlive) return;
        
        // Render thruster particles first (behind ship)
        this.renderThrusterParticles(ctx);
        
        // Apply invulnerability flashing effect
        if (this.isInvulnerable && Math.floor(Date.now() / 100) % 2) {
            ctx.globalAlpha = 0.5;
        }
        
        // Apply hit flash effect
        if (this.hitFlash > 0) {
            ctx.fillStyle = `rgba(255, 255, 255, ${this.hitFlash})`;
            ctx.fillRect(this.position.x - 2, this.position.y - 2, this.size.x + 4, this.size.y + 4);
        }
        
        // Render ship body
        this.renderShip(ctx);
        
        // Reset alpha
        ctx.globalAlpha = 1;
    }

    renderThrusterParticles(ctx) {
        this.thrusterParticles.forEach(particle => {
            ctx.fillStyle = particle.color;
            ctx.globalAlpha = particle.life;
            ctx.fillRect(particle.x, particle.y, particle.size, particle.size);
        });
        ctx.globalAlpha = 1;
    }

    renderShip(ctx) {
        // Simple pixel art style ship
        const x = Math.floor(this.position.x);
        const y = Math.floor(this.position.y);
        
        // Ship body (cyan)
        ctx.fillStyle = COLORS.PLAYER;
        ctx.fillRect(x + 8, y + 8, 16, 8);
        ctx.fillRect(x + 16, y + 4, 8, 16);
        ctx.fillRect(x + 24, y + 8, 8, 8);
        
        // Ship details (white)
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(x + 20, y + 10, 2, 4);
        ctx.fillRect(x + 26, y + 10, 2, 4);
        
        // Engine glow
        if (this.engineGlow > 0.1) {
            ctx.fillStyle = `rgba(0, 170, 255, ${this.engineGlow})`;
            ctx.fillRect(x + 4, y + 10, 4, 4);
            ctx.fillRect(x + 2, y + 11, 2, 2);
        }
        
        // Weapon indicator
        if (this.currentWeapon.type !== 'basic') {
            ctx.fillStyle = '#00ff00';
            ctx.fillRect(x + 28, y + 6, 2, 2);
            ctx.fillRect(x + 28, y + 16, 2, 2);
        }
    }
}
