// Boss battle system for Space Shooter

class WeakPoint {
    constructor(x, y, health = 10, size = 20) {
        this.position = new Vector2(x, y);
        this.health = health;
        this.maxHealth = health;
        this.size = size;
        this.radius = size / 2;
        this.isAlive = true;
        this.isVulnerable = true;
        
        // Visual effects
        this.hitFlash = 0;
        this.glowIntensity = 0;
        this.shieldActive = false;
        this.shieldTimer = 0;
        
        // Damage tracking
        this.damageNumbers = [];
    }

    update(deltaTime) {
        if (!this.isAlive) return;
        
        // Update timers
        if (this.hitFlash > 0) {
            this.hitFlash -= deltaTime * 0.01;
        }
        
        if (this.shieldActive) {
            this.shieldTimer -= deltaTime;
            if (this.shieldTimer <= 0) {
                this.shieldActive = false;
                this.isVulnerable = true;
            }
        }
        
        // Update glow effect
        this.glowIntensity = (Math.sin(Date.now() * 0.01) + 1) * 0.5;
        
        // Update damage numbers
        this.damageNumbers.forEach(dmg => {
            dmg.y -= deltaTime * 0.05;
            dmg.life -= deltaTime * 0.002;
        });
        this.damageNumbers = this.damageNumbers.filter(dmg => dmg.life > 0);
    }

    takeDamage(damage) {
        if (!this.isAlive || !this.isVulnerable || this.shieldActive) return false;
        
        this.health -= damage;
        this.hitFlash = 1.0;
        
        // Add damage number
        this.damageNumbers.push({
            x: this.position.x + Utils.random(-10, 10),
            y: this.position.y - 10,
            damage: damage,
            life: 1.0
        });
        
        // Create hit sparks
        if (window.particleSystem) {
            window.particleSystem.createSparks(
                this.position.x,
                this.position.y,
                Math.PI,
                { count: 5, speed: 3 }
            );
        }
        
        if (this.health <= 0) {
            this.destroy();
            return true; // Weak point destroyed
        }
        
        return false;
    }

    activateShield(duration = 3000) {
        this.shieldActive = true;
        this.shieldTimer = duration;
        this.isVulnerable = false;
    }

    destroy() {
        this.isAlive = false;
        
        // Create destruction effect
        if (window.particleSystem) {
            window.particleSystem.createExplosion(
                this.position.x,
                this.position.y,
                { count: 20, size: 4, colors: ['#ff0000', '#ffff00', '#ffffff'] }
            );
        }
    }

    render(ctx) {
        if (!this.isAlive) return;
        
        // Render glow
        const glowSize = this.size + this.glowIntensity * 10;
        ctx.fillStyle = '#ff000040';
        ctx.fillRect(
            this.position.x - glowSize / 2,
            this.position.y - glowSize / 2,
            glowSize,
            glowSize
        );
        
        // Render weak point
        ctx.fillStyle = this.shieldActive ? '#00ffff' : '#ff0000';
        ctx.fillRect(
            this.position.x - this.size / 2,
            this.position.y - this.size / 2,
            this.size,
            this.size
        );
        
        // Render hit flash
        if (this.hitFlash > 0) {
            ctx.fillStyle = `rgba(255, 255, 255, ${this.hitFlash})`;
            ctx.fillRect(
                this.position.x - this.size / 2 - 2,
                this.position.y - this.size / 2 - 2,
                this.size + 4,
                this.size + 4
            );
        }
        
        // Render shield effect
        if (this.shieldActive) {
            ctx.strokeStyle = '#00ffff';
            ctx.lineWidth = 2;
            ctx.strokeRect(
                this.position.x - this.size / 2 - 5,
                this.position.y - this.size / 2 - 5,
                this.size + 10,
                this.size + 10
            );
        }
        
        // Render health bar
        this.renderHealthBar(ctx);
        
        // Render damage numbers
        this.renderDamageNumbers(ctx);
    }

    renderHealthBar(ctx) {
        const barWidth = this.size;
        const barHeight = 4;
        const x = this.position.x - barWidth / 2;
        const y = this.position.y - this.size / 2 - 10;
        
        // Background
        ctx.fillStyle = '#333333';
        ctx.fillRect(x, y, barWidth, barHeight);
        
        // Health
        const healthPercent = this.health / this.maxHealth;
        ctx.fillStyle = healthPercent > 0.5 ? '#00ff00' : healthPercent > 0.25 ? '#ffff00' : '#ff0000';
        ctx.fillRect(x, y, barWidth * healthPercent, barHeight);
        
        // Border
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 1;
        ctx.strokeRect(x, y, barWidth, barHeight);
    }

    renderDamageNumbers(ctx) {
        this.damageNumbers.forEach(dmg => {
            ctx.fillStyle = `rgba(255, 255, 0, ${dmg.life})`;
            ctx.font = 'bold 12px monospace';
            ctx.textAlign = 'center';
            ctx.fillText(dmg.damage.toString(), dmg.x, dmg.y);
        });
    }
}

class Boss {
    constructor(stageNumber) {
        this.stageNumber = stageNumber;
        this.position = new Vector2(GAME_CONFIG.CANVAS_WIDTH + 100, GAME_CONFIG.CANVAS_HEIGHT / 2);
        this.velocity = new Vector2(0, 0);
        this.size = new Vector2(120, 80);
        this.radius = 40;
        
        // Boss stats
        this.health = 100;
        this.maxHealth = 100;
        this.isAlive = true;
        this.scoreValue = 5000;
        
        // Weak points
        this.weakPoints = [];
        this.weakPointsDestroyed = 0;
        
        // Combat properties
        this.attackTimer = 0;
        this.attackPattern = 0;
        this.phase = 1;
        this.maxPhases = 3;
        
        // Movement
        this.movementTimer = 0;
        this.targetPosition = this.position.copy();
        this.entryComplete = false;
        
        // Visual effects
        this.hitFlash = 0;
        this.engineParticles = [];
        
        this.initializeBoss();
        
        console.log(`Boss ${stageNumber} created`);
    }

    initializeBoss() {
        switch (this.stageNumber) {
            case 1:
                this.initializeAsteroidBoss();
                break;
            case 2:
                this.initializeStationBoss();
                break;
            case 3:
                this.initializeNebulaBoss();
                break;
            case 4:
                this.initializeFleetBoss();
                break;
            case 5:
                this.initializeCoreBoss();
                break;
        }
        
        // Set target position for entry
        this.targetPosition = new Vector2(GAME_CONFIG.CANVAS_WIDTH - 150, GAME_CONFIG.CANVAS_HEIGHT / 2);
    }

    initializeAsteroidBoss() {
        this.name = 'ASTEROID CRUSHER';
        this.size = new Vector2(100, 60);
        this.health = 80;
        this.maxHealth = 80;
        
        // Create weak points
        this.weakPoints = [
            new WeakPoint(this.position.x - 30, this.position.y - 20, 15),
            new WeakPoint(this.position.x - 30, this.position.y + 20, 15),
            new WeakPoint(this.position.x - 60, this.position.y, 20)
        ];
    }

    initializeStationBoss() {
        this.name = 'DEFENSE PLATFORM';
        this.size = new Vector2(140, 100);
        this.health = 120;
        this.maxHealth = 120;
        
        this.weakPoints = [
            new WeakPoint(this.position.x - 40, this.position.y - 30, 18),
            new WeakPoint(this.position.x - 40, this.position.y + 30, 18),
            new WeakPoint(this.position.x - 70, this.position.y - 15, 15),
            new WeakPoint(this.position.x - 70, this.position.y + 15, 15)
        ];
    }

    initializeNebulaBoss() {
        this.name = 'STORM LEVIATHAN';
        this.size = new Vector2(160, 90);
        this.health = 150;
        this.maxHealth = 150;
        
        this.weakPoints = [
            new WeakPoint(this.position.x - 50, this.position.y, 25),
            new WeakPoint(this.position.x - 80, this.position.y - 25, 20),
            new WeakPoint(this.position.x - 80, this.position.y + 25, 20),
            new WeakPoint(this.position.x - 30, this.position.y - 35, 15),
            new WeakPoint(this.position.x - 30, this.position.y + 35, 15)
        ];
    }

    initializeFleetBoss() {
        this.name = 'DREADNOUGHT';
        this.size = new Vector2(200, 120);
        this.health = 200;
        this.maxHealth = 200;
        
        this.weakPoints = [
            new WeakPoint(this.position.x - 60, this.position.y - 40, 22),
            new WeakPoint(this.position.x - 60, this.position.y + 40, 22),
            new WeakPoint(this.position.x - 100, this.position.y - 20, 18),
            new WeakPoint(this.position.x - 100, this.position.y + 20, 18),
            new WeakPoint(this.position.x - 80, this.position.y, 30)
        ];
    }

    initializeCoreBoss() {
        this.name = 'FORTRESS CORE';
        this.size = new Vector2(180, 140);
        this.health = 250;
        this.maxHealth = 250;
        
        this.weakPoints = [
            new WeakPoint(this.position.x - 40, this.position.y, 35),
            new WeakPoint(this.position.x - 70, this.position.y - 30, 25),
            new WeakPoint(this.position.x - 70, this.position.y + 30, 25),
            new WeakPoint(this.position.x - 90, this.position.y - 50, 20),
            new WeakPoint(this.position.x - 90, this.position.y + 50, 20),
            new WeakPoint(this.position.x - 90, this.position.y, 20)
        ];
    }

    update(deltaTime) {
        if (!this.isAlive) return;
        
        // Update timers
        this.updateTimers(deltaTime);
        
        // Update movement
        this.updateMovement(deltaTime);
        
        // Update weak points
        this.updateWeakPoints(deltaTime);
        
        // Update combat
        this.updateCombat(deltaTime);
        
        // Update visual effects
        this.updateEffects(deltaTime);
        
        // Check phase transitions
        this.checkPhaseTransition();
    }

    updateTimers(deltaTime) {
        this.movementTimer += deltaTime;
        this.attackTimer += deltaTime;
        
        if (this.hitFlash > 0) {
            this.hitFlash -= deltaTime * 0.01;
        }
    }

    updateMovement(deltaTime) {
        if (!this.entryComplete) {
            // Entry movement
            const direction = this.targetPosition.subtract(this.position);
            if (direction.magnitude() > 5) {
                this.velocity = direction.normalize().multiply(2);
            } else {
                this.velocity = new Vector2(0, 0);
                this.entryComplete = true;
            }
        } else {
            // Boss movement patterns
            this.updateBossMovement(deltaTime);
        }
        
        // Apply velocity
        this.position.x += this.velocity.x * deltaTime * 0.1;
        this.position.y += this.velocity.y * deltaTime * 0.1;
        
        // Update weak point positions
        this.updateWeakPointPositions();
        
        // Create engine particles
        this.createEngineParticles();
    }

    updateBossMovement(deltaTime) {
        switch (this.phase) {
            case 1:
                // Slow vertical movement
                this.velocity.y = Math.sin(this.movementTimer * 0.001) * 1.5;
                break;
            case 2:
                // More aggressive movement
                this.velocity.y = Math.sin(this.movementTimer * 0.002) * 2.5;
                this.velocity.x = Math.cos(this.movementTimer * 0.0015) * 1;
                break;
            case 3:
                // Erratic movement
                this.velocity.y = Math.sin(this.movementTimer * 0.003) * 3;
                this.velocity.x = Math.cos(this.movementTimer * 0.002) * 1.5;
                break;
        }
        
        // Keep boss in bounds
        this.position.y = Utils.clamp(this.position.y, 50, GAME_CONFIG.CANVAS_HEIGHT - 50);
        this.position.x = Utils.clamp(this.position.x, GAME_CONFIG.CANVAS_WIDTH - 200, GAME_CONFIG.CANVAS_WIDTH - 100);
    }

    updateWeakPointPositions() {
        // Update weak point positions relative to boss
        const baseX = this.position.x;
        const baseY = this.position.y;
        
        this.weakPoints.forEach((wp, index) => {
            // Calculate relative position based on boss type and index
            const relativePos = this.getWeakPointRelativePosition(index);
            wp.position.x = baseX + relativePos.x;
            wp.position.y = baseY + relativePos.y;
        });
    }

    getWeakPointRelativePosition(index) {
        // Return relative positions based on boss type
        switch (this.stageNumber) {
            case 1:
                const pos1 = [{ x: -30, y: -20 }, { x: -30, y: 20 }, { x: -60, y: 0 }];
                return pos1[index] || { x: 0, y: 0 };
            case 2:
                const pos2 = [{ x: -40, y: -30 }, { x: -40, y: 30 }, { x: -70, y: -15 }, { x: -70, y: 15 }];
                return pos2[index] || { x: 0, y: 0 };
            case 3:
                const pos3 = [{ x: -50, y: 0 }, { x: -80, y: -25 }, { x: -80, y: 25 }, { x: -30, y: -35 }, { x: -30, y: 35 }];
                return pos3[index] || { x: 0, y: 0 };
            case 4:
                const pos4 = [{ x: -60, y: -40 }, { x: -60, y: 40 }, { x: -100, y: -20 }, { x: -100, y: 20 }, { x: -80, y: 0 }];
                return pos4[index] || { x: 0, y: 0 };
            case 5:
                const pos5 = [{ x: -40, y: 0 }, { x: -70, y: -30 }, { x: -70, y: 30 }, { x: -90, y: -50 }, { x: -90, y: 50 }, { x: -90, y: 0 }];
                return pos5[index] || { x: 0, y: 0 };
            default:
                return { x: 0, y: 0 };
        }
    }

    updateWeakPoints(deltaTime) {
        this.weakPoints.forEach(wp => wp.update(deltaTime));
        this.weakPoints = this.weakPoints.filter(wp => wp.isAlive);
    }

    updateCombat(deltaTime) {
        const attackDelay = Math.max(1000, 3000 - (this.phase - 1) * 500);
        
        if (this.attackTimer >= attackDelay) {
            this.performAttack();
            this.attackTimer = 0;
        }
    }

    performAttack() {
        switch (this.attackPattern) {
            case 0:
                this.straightShotAttack();
                break;
            case 1:
                this.spreadShotAttack();
                break;
            case 2:
                this.homingMissileAttack();
                break;
            case 3:
                this.laserBeamAttack();
                break;
        }
        
        this.attackPattern = (this.attackPattern + 1) % (this.phase + 1);
    }

    straightShotAttack() {
        if (!window.gameEngine) return;
        
        const projectile = new EnemyProjectile(
            this.position.x - 50,
            this.position.y,
            -6, 0, 2, 'straight'
        );
        
        window.gameEngine.projectiles.push(projectile);
    }

    spreadShotAttack() {
        if (!window.gameEngine) return;
        
        const angles = [-0.3, -0.15, 0, 0.15, 0.3];
        angles.forEach(angle => {
            const projectile = new EnemyProjectile(
                this.position.x - 50,
                this.position.y,
                Math.cos(angle) * -5,
                Math.sin(angle) * -5,
                1, 'straight'
            );
            window.gameEngine.projectiles.push(projectile);
        });
    }

    homingMissileAttack() {
        if (!window.gameEngine) return;
        
        const missile = new HomingMissile(
            this.position.x - 50,
            this.position.y,
            'enemy', 3
        );
        
        window.gameEngine.projectiles.push(missile);
    }

    laserBeamAttack() {
        if (!window.gameEngine) return;
        
        const laser = new LaserBeam(
            this.position.x - 50,
            this.position.y,
            'enemy', 4
        );
        
        window.gameEngine.projectiles.push(laser);
    }

    updateEffects(deltaTime) {
        // Update engine particles
        this.engineParticles.forEach(particle => {
            particle.x += particle.vx * deltaTime * 0.1;
            particle.y += particle.vy * deltaTime * 0.1;
            particle.life -= particle.decay;
        });
        
        this.engineParticles = this.engineParticles.filter(particle => particle.life > 0);
    }

    createEngineParticles() {
        const particleX = this.position.x + this.size.x / 2;
        const particleY = this.position.y + Utils.random(-10, 10);
        
        this.engineParticles.push({
            x: particleX,
            y: particleY,
            vx: Utils.random(2, 4),
            vy: Utils.random(-1, 1),
            life: 1.0,
            decay: 0.04,
            size: Utils.random(2, 4),
            color: Utils.randomChoice(['#ff4400', '#ff6600', '#ff8800'])
        });
        
        if (this.engineParticles.length > 15) {
            this.engineParticles.shift();
        }
    }

    checkPhaseTransition() {
        const weakPointsRemaining = this.weakPoints.length;
        const totalWeakPoints = this.getInitialWeakPointCount();
        const destroyedPercent = (totalWeakPoints - weakPointsRemaining) / totalWeakPoints;
        
        if (destroyedPercent >= 0.33 && this.phase === 1) {
            this.enterPhase(2);
        } else if (destroyedPercent >= 0.66 && this.phase === 2) {
            this.enterPhase(3);
        } else if (weakPointsRemaining === 0 && this.phase === 3) {
            this.destroy();
        }
    }

    getInitialWeakPointCount() {
        switch (this.stageNumber) {
            case 1: return 3;
            case 2: return 4;
            case 3: return 5;
            case 4: return 5;
            case 5: return 6;
            default: return 3;
        }
    }

    enterPhase(newPhase) {
        this.phase = newPhase;
        
        // Activate shields on remaining weak points temporarily
        this.weakPoints.forEach(wp => {
            wp.activateShield(2000);
        });
        
        // Create phase transition effect
        if (window.particleSystem) {
            window.particleSystem.createExplosion(
                this.position.x,
                this.position.y,
                { count: 30, size: 5, colors: ['#ffff00', '#ff8800', '#ffffff'] }
            );
        }
        
        console.log(`Boss entering phase ${newPhase}`);
    }

    takeDamage(damage, hitPoint) {
        // Check if hit point is a weak point
        let weakPointHit = false;
        this.weakPoints.forEach(wp => {
            if (wp.position.distance(hitPoint) < wp.radius) {
                if (wp.takeDamage(damage)) {
                    this.weakPointsDestroyed++;
                }
                weakPointHit = true;
            }
        });
        
        if (!weakPointHit) {
            // Hit main body - reduced damage
            this.health -= Math.ceil(damage * 0.1);
            this.hitFlash = 0.5;
        }
        
        if (this.health <= 0 && this.weakPoints.length === 0) {
            this.destroy();
        }
    }

    destroy() {
        this.isAlive = false;
        
        // Create massive explosion
        if (window.particleSystem) {
            for (let i = 0; i < 50; i++) {
                const x = this.position.x + Utils.random(-this.size.x / 2, this.size.x / 2);
                const y = this.position.y + Utils.random(-this.size.y / 2, this.size.y / 2);
                window.particleSystem.createExplosion(x, y, {
                    count: 15,
                    size: 6,
                    colors: ['#ff0000', '#ffff00', '#ffffff', '#ff8800']
                });
            }
        }
        
        console.log(`Boss ${this.stageNumber} destroyed`);
    }

    render(ctx) {
        if (!this.isAlive) return;
        
        // Render engine particles
        this.renderEngineParticles(ctx);
        
        // Render boss body
        this.renderBossBody(ctx);
        
        // Render weak points
        this.weakPoints.forEach(wp => wp.render(ctx));
        
        // Render boss health bar
        this.renderBossHealthBar(ctx);
        
        // Render boss name
        this.renderBossName(ctx);
    }

    renderEngineParticles(ctx) {
        this.engineParticles.forEach(particle => {
            ctx.fillStyle = particle.color;
            ctx.globalAlpha = particle.life;
            ctx.fillRect(particle.x, particle.y, particle.size, particle.size);
        });
        ctx.globalAlpha = 1;
    }

    renderBossBody(ctx) {
        const x = Math.floor(this.position.x - this.size.x / 2);
        const y = Math.floor(this.position.y - this.size.y / 2);
        
        // Apply hit flash
        if (this.hitFlash > 0) {
            ctx.fillStyle = `rgba(255, 255, 255, ${this.hitFlash})`;
            ctx.fillRect(x - 2, y - 2, this.size.x + 4, this.size.y + 4);
        }
        
        // Main body
        ctx.fillStyle = '#444444';
        ctx.fillRect(x, y, this.size.x, this.size.y);
        
        // Details based on boss type
        this.renderBossDetails(ctx, x, y);
        
        // Border
        ctx.strokeStyle = '#666666';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, this.size.x, this.size.y);
    }

    renderBossDetails(ctx, x, y) {
        // Add boss-specific visual details
        ctx.fillStyle = '#666666';
        
        switch (this.stageNumber) {
            case 1: // Asteroid Boss
                ctx.fillRect(x + 10, y + 10, this.size.x - 20, this.size.y - 20);
                break;
            case 2: // Station Boss
                ctx.fillRect(x + 20, y + 20, this.size.x - 40, this.size.y - 40);
                ctx.fillStyle = '#ffff00';
                for (let i = 0; i < 8; i++) {
                    const lx = x + Utils.random(0, this.size.x);
                    const ly = y + Utils.random(0, this.size.y);
                    ctx.fillRect(lx, ly, 2, 2);
                }
                break;
            case 3: // Nebula Boss
                ctx.fillStyle = '#ff00ff40';
                ctx.fillRect(x, y, this.size.x, this.size.y);
                break;
            case 4: // Fleet Boss
                ctx.fillRect(x + 15, y + 15, this.size.x - 30, this.size.y - 30);
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(x + this.size.x - 20, y + this.size.y / 2 - 5, 15, 10);
                break;
            case 5: // Core Boss
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(x + this.size.x / 2 - 10, y + this.size.y / 2 - 10, 20, 20);
                break;
        }
    }

    renderBossHealthBar(ctx) {
        const barWidth = 200;
        const barHeight = 8;
        const x = GAME_CONFIG.CANVAS_WIDTH / 2 - barWidth / 2;
        const y = 30;
        
        // Background
        ctx.fillStyle = '#333333';
        ctx.fillRect(x, y, barWidth, barHeight);
        
        // Health
        const healthPercent = this.health / this.maxHealth;
        const healthColor = healthPercent > 0.5 ? '#00ff00' : healthPercent > 0.25 ? '#ffff00' : '#ff0000';
        ctx.fillStyle = healthColor;
        ctx.fillRect(x, y, barWidth * healthPercent, barHeight);
        
        // Border
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, barWidth, barHeight);
        
        // Phase indicator
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 12px monospace';
        ctx.textAlign = 'center';
        ctx.fillText(`PHASE ${this.phase}`, GAME_CONFIG.CANVAS_WIDTH / 2, y + 25);
    }

    renderBossName(ctx) {
        ctx.fillStyle = '#ffff00';
        ctx.font = 'bold 16px monospace';
        ctx.textAlign = 'center';
        ctx.fillText(this.name, GAME_CONFIG.CANVAS_WIDTH / 2, 20);
    }
}
