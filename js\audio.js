// Audio system for Space Shooter

class AudioManager {
    constructor() {
        this.sounds = {};
        this.music = {};
        this.soundEnabled = true;
        this.musicEnabled = true;
        this.masterVolume = 0.7;
        this.soundVolume = 0.8;
        this.musicVolume = 0.5;
        
        // Audio context for better control
        this.audioContext = null;
        this.gainNode = null;
        
        this.initializeAudioContext();
        this.createSounds();
        
        console.log('Audio Manager initialized');
    }

    initializeAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.gainNode = this.audioContext.createGain();
            this.gainNode.connect(this.audioContext.destination);
            this.gainNode.gain.value = this.masterVolume;
        } catch (error) {
            console.warn('Web Audio API not supported, falling back to HTML5 audio');
        }
    }

    createSounds() {
        // Create sound effects using Web Audio API or fallback to HTML5 audio
        this.createSound('shoot', this.generateShootSound());
        this.createSound('explosion', this.generateExplosionSound());
        this.createSound('powerUp', this.generatePowerUpSound());
        this.createSound('playerHit', this.generatePlayerHitSound());
        this.createSound('enemyHit', this.generateEnemyHitSound());
        this.createSound('bomb', this.generateBombSound());
        this.createSound('laser', this.generateLaserSound());
        this.createSound('missile', this.generateMissileSound());
        
        // Background music
        this.createMusic('stage1', this.generateBackgroundMusic());
        this.createMusic('boss', this.generateBossMusic());
        this.createMusic('menu', this.generateMenuMusic());
    }

    createSound(name, audioData) {
        if (this.audioContext && audioData) {
            this.sounds[name] = audioData;
        } else {
            // Fallback to HTML5 audio elements
            const audio = document.getElementById(name + 'Sound');
            if (audio) {
                this.sounds[name] = audio;
            }
        }
    }

    createMusic(name, audioData) {
        if (this.audioContext && audioData) {
            this.music[name] = audioData;
        } else {
            // Fallback to HTML5 audio
            const audio = document.getElementById('backgroundMusic');
            if (audio) {
                this.music[name] = audio;
            }
        }
    }

    // Generate sound effects using Web Audio API
    generateShootSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.1;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const frequency = 800 - (t * 600);
            data[i] = Math.sin(2 * Math.PI * frequency * t) * Math.exp(-t * 10) * 0.3;
        }
        
        return buffer;
    }

    generateExplosionSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.5;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const noise = (Math.random() - 0.5) * 2;
            const envelope = Math.exp(-t * 3);
            data[i] = noise * envelope * 0.5;
        }
        
        return buffer;
    }

    generatePowerUpSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.3;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const frequency = 400 + (t * 800);
            const envelope = Math.exp(-t * 2);
            data[i] = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.4;
        }
        
        return buffer;
    }

    generatePlayerHitSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.2;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const frequency = 200 - (t * 150);
            const envelope = Math.exp(-t * 5);
            data[i] = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.6;
        }
        
        return buffer;
    }

    generateEnemyHitSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.15;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const frequency = 600 - (t * 400);
            const envelope = Math.exp(-t * 8);
            data[i] = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.3;
        }
        
        return buffer;
    }

    generateBombSound() {
        if (!this.audioContext) return null;
        
        const duration = 1.0;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const noise = (Math.random() - 0.5) * 2;
            const lowFreq = Math.sin(2 * Math.PI * 60 * t);
            const envelope = Math.exp(-t * 1.5);
            data[i] = (noise * 0.7 + lowFreq * 0.3) * envelope * 0.8;
        }
        
        return buffer;
    }

    generateLaserSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.2;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const frequency = 1200 + Math.sin(t * 100) * 200;
            const envelope = 1 - (t / duration);
            data[i] = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.4;
        }
        
        return buffer;
    }

    generateMissileSound() {
        if (!this.audioContext) return null;
        
        const duration = 0.4;
        const sampleRate = this.audioContext.sampleRate;
        const buffer = this.audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);
        
        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const frequency = 300 + Math.sin(t * 50) * 100;
            const envelope = Math.exp(-t * 2);
            data[i] = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.5;
        }
        
        return buffer;
    }

    generateBackgroundMusic() {
        // For now, return null - in a real implementation, you'd generate or load music
        return null;
    }

    generateBossMusic() {
        return null;
    }

    generateMenuMusic() {
        return null;
    }

    playSound(soundName, volume = 1.0) {
        if (!this.soundEnabled) return;
        
        const sound = this.sounds[soundName];
        if (!sound) return;
        
        try {
            if (this.audioContext && sound instanceof AudioBuffer) {
                // Web Audio API
                const source = this.audioContext.createBufferSource();
                const gainNode = this.audioContext.createGain();
                
                source.buffer = sound;
                gainNode.gain.value = volume * this.soundVolume;
                
                source.connect(gainNode);
                gainNode.connect(this.gainNode);
                
                source.start();
            } else if (sound instanceof HTMLAudioElement) {
                // HTML5 Audio
                const audioClone = sound.cloneNode();
                audioClone.volume = volume * this.soundVolume;
                audioClone.play().catch(e => console.warn('Audio play failed:', e));
            }
        } catch (error) {
            console.warn('Failed to play sound:', soundName, error);
        }
    }

    playMusic(musicName, loop = true) {
        if (!this.musicEnabled) return;
        
        this.stopMusic();
        
        const music = this.music[musicName];
        if (!music) return;
        
        try {
            if (music instanceof HTMLAudioElement) {
                music.volume = this.musicVolume;
                music.loop = loop;
                music.currentTime = 0;
                music.play().catch(e => console.warn('Music play failed:', e));
                this.currentMusic = music;
            }
        } catch (error) {
            console.warn('Failed to play music:', musicName, error);
        }
    }

    stopMusic() {
        if (this.currentMusic) {
            this.currentMusic.pause();
            this.currentMusic.currentTime = 0;
            this.currentMusic = null;
        }
    }

    pauseMusic() {
        if (this.currentMusic) {
            this.currentMusic.pause();
        }
    }

    resumeMusic() {
        if (this.currentMusic) {
            this.currentMusic.play().catch(e => console.warn('Music resume failed:', e));
        }
    }

    setMasterVolume(volume) {
        this.masterVolume = Utils.clamp(volume, 0, 1);
        if (this.gainNode) {
            this.gainNode.gain.value = this.masterVolume;
        }
    }

    setSoundVolume(volume) {
        this.soundVolume = Utils.clamp(volume, 0, 1);
    }

    setMusicVolume(volume) {
        this.musicVolume = Utils.clamp(volume, 0, 1);
        if (this.currentMusic) {
            this.currentMusic.volume = this.musicVolume;
        }
    }

    toggleSound() {
        this.soundEnabled = !this.soundEnabled;
        return this.soundEnabled;
    }

    toggleMusic() {
        this.musicEnabled = !this.musicEnabled;
        if (!this.musicEnabled) {
            this.stopMusic();
        }
        return this.musicEnabled;
    }

    // Resume audio context on user interaction (required by browsers)
    resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume().then(() => {
                console.log('Audio context resumed');
            });
        }
    }
}

// Initialize audio manager
document.addEventListener('DOMContentLoaded', () => {
    window.audioManager = new AudioManager();
    
    // Resume audio context on first user interaction
    const resumeAudio = () => {
        window.audioManager.resumeAudioContext();
        document.removeEventListener('click', resumeAudio);
        document.removeEventListener('keydown', resumeAudio);
    };
    
    document.addEventListener('click', resumeAudio);
    document.addEventListener('keydown', resumeAudio);
});
