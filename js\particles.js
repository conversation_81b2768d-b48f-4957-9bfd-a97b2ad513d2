// Particle system for visual effects in Space Shooter

class Particle {
    constructor(x, y, velocityX = 0, velocityY = 0, options = {}) {
        this.x = x;
        this.y = y;
        this.vx = velocityX;
        this.vy = velocityY;
        
        // Particle properties
        this.life = options.life || 1.0;
        this.maxLife = this.life;
        this.decay = options.decay || 0.02;
        this.size = options.size || 2;
        this.initialSize = this.size;
        this.color = options.color || '#ffffff';
        this.gravity = options.gravity || 0;
        this.friction = options.friction || 1;
        this.bounce = options.bounce || 0;
        this.rotation = options.rotation || 0;
        this.rotationSpeed = options.rotationSpeed || 0;
        
        // Visual effects
        this.glow = options.glow || false;
        this.trail = options.trail || false;
        this.trailPositions = [];
        this.maxTrailLength = 5;
        
        // Animation
        this.scaleOverTime = options.scaleOverTime || false;
        this.fadeOverTime = options.fadeOverTime !== false;
        this.colorShift = options.colorShift || null;
    }

    update(deltaTime) {
        // Update position
        this.x += this.vx * deltaTime * 0.1;
        this.y += this.vy * deltaTime * 0.1;
        
        // Apply gravity
        this.vy += this.gravity * deltaTime * 0.1;
        
        // Apply friction
        this.vx *= this.friction;
        this.vy *= this.friction;
        
        // Update rotation
        this.rotation += this.rotationSpeed * deltaTime * 0.1;
        
        // Store trail positions
        if (this.trail) {
            this.trailPositions.push({ x: this.x, y: this.y });
            if (this.trailPositions.length > this.maxTrailLength) {
                this.trailPositions.shift();
            }
        }
        
        // Update life
        this.life -= this.decay;
        
        // Update size based on life
        if (this.scaleOverTime) {
            this.size = this.initialSize * (this.life / this.maxLife);
        }
        
        // Boundary collision with bounce
        if (this.bounce > 0) {
            if (this.x <= 0 || this.x >= GAME_CONFIG.CANVAS_WIDTH) {
                this.vx *= -this.bounce;
                this.x = Utils.clamp(this.x, 0, GAME_CONFIG.CANVAS_WIDTH);
            }
            if (this.y <= 0 || this.y >= GAME_CONFIG.CANVAS_HEIGHT) {
                this.vy *= -this.bounce;
                this.y = Utils.clamp(this.y, 0, GAME_CONFIG.CANVAS_HEIGHT);
            }
        }
    }

    render(ctx) {
        if (this.life <= 0) return;
        
        const alpha = this.fadeOverTime ? this.life / this.maxLife : 1;
        
        // Render trail
        if (this.trail && this.trailPositions.length > 1) {
            this.renderTrail(ctx, alpha);
        }
        
        ctx.save();
        
        // Apply rotation
        if (this.rotation !== 0) {
            ctx.translate(this.x, this.y);
            ctx.rotate(this.rotation);
            ctx.translate(-this.x, -this.y);
        }
        
        // Set color with alpha
        const color = this.getCurrentColor();
        ctx.fillStyle = this.addAlphaToColor(color, alpha);
        
        // Apply glow effect
        if (this.glow) {
            ctx.shadowColor = color;
            ctx.shadowBlur = this.size * 2;
        }
        
        // Render particle
        ctx.fillRect(
            this.x - this.size / 2,
            this.y - this.size / 2,
            this.size,
            this.size
        );
        
        ctx.restore();
        ctx.shadowBlur = 0;
    }

    renderTrail(ctx, alpha) {
        for (let i = 0; i < this.trailPositions.length - 1; i++) {
            const pos = this.trailPositions[i];
            const trailAlpha = (i / this.trailPositions.length) * alpha * 0.5;
            const trailSize = this.size * (i / this.trailPositions.length);
            
            ctx.fillStyle = this.addAlphaToColor(this.color, trailAlpha);
            ctx.fillRect(
                pos.x - trailSize / 2,
                pos.y - trailSize / 2,
                trailSize,
                trailSize
            );
        }
    }

    getCurrentColor() {
        if (this.colorShift) {
            const progress = 1 - (this.life / this.maxLife);
            return this.interpolateColor(this.color, this.colorShift, progress);
        }
        return this.color;
    }

    addAlphaToColor(color, alpha) {
        if (color.startsWith('#')) {
            const hex = color.slice(1);
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }
        return color;
    }

    interpolateColor(color1, color2, factor) {
        // Simple color interpolation for hex colors
        if (color1.startsWith('#') && color2.startsWith('#')) {
            const hex1 = color1.slice(1);
            const hex2 = color2.slice(1);
            
            const r1 = parseInt(hex1.substr(0, 2), 16);
            const g1 = parseInt(hex1.substr(2, 2), 16);
            const b1 = parseInt(hex1.substr(4, 2), 16);
            
            const r2 = parseInt(hex2.substr(0, 2), 16);
            const g2 = parseInt(hex2.substr(2, 2), 16);
            const b2 = parseInt(hex2.substr(4, 2), 16);
            
            const r = Math.round(r1 + (r2 - r1) * factor);
            const g = Math.round(g1 + (g2 - g1) * factor);
            const b = Math.round(b1 + (b2 - b1) * factor);
            
            return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
        }
        return color1;
    }

    isDead() {
        return this.life <= 0;
    }
}

class ParticleSystem {
    constructor() {
        this.particles = [];
        this.maxParticles = GAME_CONFIG.MAX_PARTICLES;
    }

    addParticle(particle) {
        if (this.particles.length >= this.maxParticles) {
            this.particles.shift(); // Remove oldest particle
        }
        this.particles.push(particle);
    }

    createExplosion(x, y, options = {}) {
        const particleCount = options.count || 15;
        const colors = options.colors || ['#ff4400', '#ff8800', '#ffaa00', '#ffffff'];
        const speed = options.speed || 3;
        const size = options.size || 3;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (Math.PI * 2 * i) / particleCount + Utils.random(-0.5, 0.5);
            const velocity = Utils.random(speed * 0.5, speed);
            const vx = Math.cos(angle) * velocity;
            const vy = Math.sin(angle) * velocity;
            
            const particle = new Particle(x, y, vx, vy, {
                life: Utils.random(0.8, 1.2),
                decay: Utils.random(0.02, 0.04),
                size: Utils.random(size * 0.5, size),
                color: Utils.randomChoice(colors),
                gravity: Utils.random(0, 0.1),
                friction: 0.98,
                glow: true,
                scaleOverTime: true,
                colorShift: '#660000'
            });
            
            this.addParticle(particle);
        }
    }

    createSparks(x, y, direction = 0, options = {}) {
        const sparkCount = options.count || 8;
        const spread = options.spread || Math.PI / 3;
        const speed = options.speed || 4;
        
        for (let i = 0; i < sparkCount; i++) {
            const angle = direction + Utils.random(-spread / 2, spread / 2);
            const velocity = Utils.random(speed * 0.7, speed);
            const vx = Math.cos(angle) * velocity;
            const vy = Math.sin(angle) * velocity;
            
            const particle = new Particle(x, y, vx, vy, {
                life: Utils.random(0.3, 0.6),
                decay: 0.05,
                size: Utils.random(1, 2),
                color: Utils.randomChoice(['#ffff00', '#ffffff', '#ffaa00']),
                gravity: 0.2,
                friction: 0.95,
                glow: true,
                trail: true
            });
            
            this.addParticle(particle);
        }
    }

    createSmoke(x, y, options = {}) {
        const smokeCount = options.count || 5;
        const colors = ['#666666', '#888888', '#aaaaaa'];
        
        for (let i = 0; i < smokeCount; i++) {
            const vx = Utils.random(-1, 1);
            const vy = Utils.random(-2, -0.5);
            
            const particle = new Particle(x, y, vx, vy, {
                life: Utils.random(1.5, 2.5),
                decay: 0.01,
                size: Utils.random(4, 8),
                color: Utils.randomChoice(colors),
                friction: 0.99,
                scaleOverTime: false,
                glow: false
            });
            
            this.addParticle(particle);
        }
    }

    createStarfield(x, y, options = {}) {
        const starCount = options.count || 3;
        
        for (let i = 0; i < starCount; i++) {
            const vx = Utils.random(-2, -0.5);
            const vy = Utils.random(-0.5, 0.5);
            
            const particle = new Particle(x, y, vx, vy, {
                life: Utils.random(3, 5),
                decay: 0.005,
                size: Utils.random(1, 2),
                color: Utils.randomChoice(['#ffffff', '#aaaaff', '#ffaaaa']),
                friction: 1,
                glow: true
            });
            
            this.addParticle(particle);
        }
    }

    createPowerUpEffect(x, y, color = '#00ff00') {
        const particleCount = 12;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (Math.PI * 2 * i) / particleCount;
            const radius = Utils.random(20, 40);
            const targetX = x + Math.cos(angle) * radius;
            const targetY = y + Math.sin(angle) * radius;
            
            const vx = (targetX - x) * 0.1;
            const vy = (targetY - y) * 0.1;
            
            const particle = new Particle(x, y, vx, vy, {
                life: 1.0,
                decay: 0.03,
                size: Utils.random(2, 4),
                color: color,
                friction: 0.95,
                glow: true,
                scaleOverTime: true
            });
            
            this.addParticle(particle);
        }
    }

    createThrusterTrail(x, y, direction, intensity = 1) {
        if (Math.random() > intensity * 0.3) return;
        
        const angle = direction + Utils.random(-0.2, 0.2);
        const speed = Utils.random(1, 3) * intensity;
        const vx = Math.cos(angle) * speed;
        const vy = Math.sin(angle) * speed;
        
        const particle = new Particle(x, y, vx, vy, {
            life: Utils.random(0.2, 0.4),
            decay: 0.08,
            size: Utils.random(1, 2),
            color: Utils.randomChoice(['#00aaff', '#0088ff', '#ffffff']),
            friction: 0.9,
            glow: true,
            scaleOverTime: true
        });
        
        this.addParticle(particle);
    }

    update(deltaTime) {
        // Update all particles
        this.particles.forEach(particle => particle.update(deltaTime));
        
        // Remove dead particles
        this.particles = this.particles.filter(particle => !particle.isDead());
    }

    render(ctx) {
        this.particles.forEach(particle => particle.render(ctx));
    }

    clear() {
        this.particles = [];
    }

    getParticleCount() {
        return this.particles.length;
    }
}

// Global particle system instance
window.particleSystem = new ParticleSystem();
