// Input handling for Space Shooter

class InputHandler {
    constructor() {
        this.keys = {};
        this.gamepadIndex = null;
        this.gamepad = null;
        
        // Key mappings
        this.keyMappings = {
            // Movement
            'ArrowLeft': 'left',
            'KeyA': 'left',
            'ArrowRight': 'right',
            'KeyD': 'right',
            'ArrowUp': 'up',
            'KeyW': 'up',
            'ArrowDown': 'down',
            'KeyS': 'down',
            
            // Actions
            'Space': 'shoot',
            'KeyZ': 'shoot',
            'KeyX': 'bomb',
            'Escape': 'pause',
            'KeyP': 'pause'
        };
        
        // Current input state
        this.inputState = {
            left: false,
            right: false,
            up: false,
            down: false,
            shoot: false,
            bomb: false,
            pause: false
        };
        
        this.setupEventListeners();
        this.setupGamepadSupport();
        
        console.log('Input handler initialized');
    }

    setupEventListeners() {
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // Prevent default behavior for game keys
        document.addEventListener('keydown', (e) => {
            if (this.keyMappings[e.code]) {
                e.preventDefault();
            }
        });
        
        // Focus handling
        window.addEventListener('blur', () => this.resetInputState());
        window.addEventListener('focus', () => this.resetInputState());
    }

    setupGamepadSupport() {
        // Gamepad connection events
        window.addEventListener('gamepadconnected', (e) => {
            console.log('Gamepad connected:', e.gamepad.id);
            this.gamepadIndex = e.gamepad.index;
        });
        
        window.addEventListener('gamepaddisconnected', (e) => {
            console.log('Gamepad disconnected:', e.gamepad.id);
            if (this.gamepadIndex === e.gamepad.index) {
                this.gamepadIndex = null;
                this.gamepad = null;
            }
        });
    }

    handleKeyDown(event) {
        const action = this.keyMappings[event.code];
        if (action) {
            this.keys[event.code] = true;
            this.inputState[action] = true;
            
            // Handle special actions
            if (action === 'pause') {
                this.handlePauseAction();
            }
        }
    }

    handleKeyUp(event) {
        const action = this.keyMappings[event.code];
        if (action) {
            this.keys[event.code] = false;
            this.inputState[action] = false;
        }
    }

    handlePauseAction() {
        if (window.gameEngine) {
            const gameState = window.gameEngine.gameState;
            if (gameState === 'playing') {
                window.gameEngine.pauseGame();
            } else if (gameState === 'paused') {
                window.gameEngine.resumeGame();
            }
        }
    }

    updateGamepad() {
        if (this.gamepadIndex !== null) {
            const gamepads = navigator.getGamepads();
            this.gamepad = gamepads[this.gamepadIndex];
            
            if (this.gamepad) {
                this.updateGamepadInput();
            }
        }
    }

    updateGamepadInput() {
        if (!this.gamepad) return;
        
        const deadzone = 0.2;
        
        // Left stick or D-pad for movement
        const leftStickX = this.gamepad.axes[0];
        const leftStickY = this.gamepad.axes[1];
        
        // Movement input
        this.inputState.left = leftStickX < -deadzone || this.gamepad.buttons[14]?.pressed;
        this.inputState.right = leftStickX > deadzone || this.gamepad.buttons[15]?.pressed;
        this.inputState.up = leftStickY < -deadzone || this.gamepad.buttons[12]?.pressed;
        this.inputState.down = leftStickY > deadzone || this.gamepad.buttons[13]?.pressed;
        
        // Action buttons
        this.inputState.shoot = this.gamepad.buttons[0]?.pressed || // A button
                               this.gamepad.buttons[2]?.pressed || // X button
                               this.gamepad.buttons[5]?.pressed;   // Right bumper
        
        this.inputState.bomb = this.gamepad.buttons[1]?.pressed || // B button
                              this.gamepad.buttons[3]?.pressed;   // Y button
        
        this.inputState.pause = this.gamepad.buttons[9]?.pressed; // Start button
    }

    getPlayerInput() {
        // Update gamepad input
        this.updateGamepad();
        
        return {
            left: this.inputState.left,
            right: this.inputState.right,
            up: this.inputState.up,
            down: this.inputState.down,
            shoot: this.inputState.shoot,
            bomb: this.inputState.bomb
        };
    }

    isKeyPressed(keyCode) {
        return this.keys[keyCode] || false;
    }

    isActionPressed(action) {
        return this.inputState[action] || false;
    }

    resetInputState() {
        // Reset all input states
        Object.keys(this.inputState).forEach(key => {
            this.inputState[key] = false;
        });
        
        // Clear key states
        this.keys = {};
    }

    // Touch/mobile support
    setupTouchControls() {
        // Create virtual controls for mobile devices
        if ('ontouchstart' in window) {
            this.createVirtualControls();
        }
    }

    createVirtualControls() {
        // Create virtual D-pad and action buttons
        const controlsContainer = document.createElement('div');
        controlsContainer.id = 'virtualControls';
        controlsContainer.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 0;
            right: 0;
            height: 150px;
            pointer-events: none;
            z-index: 1000;
        `;
        
        // D-pad
        const dpad = this.createDPad();
        controlsContainer.appendChild(dpad);
        
        // Action buttons
        const actionButtons = this.createActionButtons();
        controlsContainer.appendChild(actionButtons);
        
        document.body.appendChild(controlsContainer);
    }

    createDPad() {
        const dpad = document.createElement('div');
        dpad.style.cssText = `
            position: absolute;
            left: 20px;
            bottom: 0;
            width: 120px;
            height: 120px;
            pointer-events: auto;
        `;
        
        const directions = [
            { name: 'up', style: 'top: 0; left: 40px; width: 40px; height: 40px;' },
            { name: 'down', style: 'bottom: 0; left: 40px; width: 40px; height: 40px;' },
            { name: 'left', style: 'top: 40px; left: 0; width: 40px; height: 40px;' },
            { name: 'right', style: 'top: 40px; right: 0; width: 40px; height: 40px;' }
        ];
        
        directions.forEach(dir => {
            const button = document.createElement('div');
            button.style.cssText = `
                position: absolute;
                ${dir.style}
                background: rgba(255, 255, 255, 0.3);
                border: 2px solid rgba(255, 255, 255, 0.5);
                border-radius: 8px;
                touch-action: none;
            `;
            
            button.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.inputState[dir.name] = true;
            });
            
            button.addEventListener('touchend', (e) => {
                e.preventDefault();
                this.inputState[dir.name] = false;
            });
            
            dpad.appendChild(button);
        });
        
        return dpad;
    }

    createActionButtons() {
        const actionContainer = document.createElement('div');
        actionContainer.style.cssText = `
            position: absolute;
            right: 20px;
            bottom: 0;
            width: 120px;
            height: 120px;
            pointer-events: auto;
        `;
        
        const buttons = [
            { name: 'shoot', style: 'bottom: 20px; right: 0; width: 50px; height: 50px;', label: 'FIRE' },
            { name: 'bomb', style: 'bottom: 0; right: 60px; width: 40px; height: 40px;', label: 'BOMB' }
        ];
        
        buttons.forEach(btn => {
            const button = document.createElement('div');
            button.style.cssText = `
                position: absolute;
                ${btn.style}
                background: rgba(255, 0, 128, 0.3);
                border: 2px solid rgba(255, 0, 128, 0.5);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 10px;
                font-weight: bold;
                touch-action: none;
            `;
            button.textContent = btn.label;
            
            button.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.inputState[btn.name] = true;
                button.style.background = 'rgba(255, 0, 128, 0.6)';
            });
            
            button.addEventListener('touchend', (e) => {
                e.preventDefault();
                this.inputState[btn.name] = false;
                button.style.background = 'rgba(255, 0, 128, 0.3)';
            });
            
            actionContainer.appendChild(button);
        });
        
        return actionContainer;
    }

    // Vibration support for gamepads
    vibrate(duration = 200, intensity = 0.5) {
        if (this.gamepad && this.gamepad.vibrationActuator) {
            this.gamepad.vibrationActuator.playEffect('dual-rumble', {
                duration: duration,
                strongMagnitude: intensity,
                weakMagnitude: intensity * 0.5
            });
        }
    }

    // Get input method being used
    getInputMethod() {
        if (this.gamepad) return 'gamepad';
        if ('ontouchstart' in window) return 'touch';
        return 'keyboard';
    }
}

// Initialize input handler
document.addEventListener('DOMContentLoaded', () => {
    window.inputHandler = new InputHandler();
    
    // Setup touch controls on mobile
    if ('ontouchstart' in window) {
        window.inputHandler.setupTouchControls();
    }
});
