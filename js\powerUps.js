// Power-up system for Space Shooter

class PowerUp {
    constructor(x, y, type = 'spreadShot') {
        this.position = new Vector2(x, y);
        this.velocity = new Vector2(-1, 0);
        this.type = type;
        this.size = new Vector2(16, 16);
        this.radius = 8;
        this.isAlive = true;
        
        // Visual properties
        this.color = '#00ff00';
        this.glowIntensity = 0;
        this.rotationAngle = 0;
        this.bobOffset = 0;
        this.bobSpeed = 0.005;
        this.bobAmplitude = 3;
        
        // Lifetime
        this.lifetime = 15000; // 15 seconds
        this.age = 0;
        this.blinkTimer = 0;
        this.isBlinking = false;
        
        // Magnetic attraction
        this.magneticRange = 80;
        this.magneticStrength = 0.1;
        
        // Initialize based on type
        this.initializeType();
        
        console.log(`PowerUp created: ${type} at (${x}, ${y})`);
    }

    initializeType() {
        switch (this.type) {
            case 'spreadShot':
                this.color = '#00ff00';
                this.icon = 'S';
                this.description = 'SPREAD SHOT';
                break;
                
            case 'rapidFire':
                this.color = '#ffff00';
                this.icon = 'R';
                this.description = 'RAPID FIRE';
                break;
                
            case 'laser':
                this.color = '#ff0000';
                this.icon = 'L';
                this.description = 'LASER BEAM';
                break;
                
            case 'homingMissile':
                this.color = '#ff8800';
                this.icon = 'H';
                this.description = 'HOMING MISSILE';
                break;
                
            case 'shield':
                this.color = '#00ffff';
                this.icon = 'D';
                this.description = 'SHIELD';
                break;
                
            case 'bomb':
                this.color = '#ff00ff';
                this.icon = 'B';
                this.description = 'SMART BOMB';
                break;
                
            case 'scoreBonus':
                this.color = '#ffffff';
                this.icon = '$';
                this.description = 'SCORE BONUS';
                break;
                
            case 'extraLife':
                this.color = '#ff0080';
                this.icon = '♦';
                this.description = 'EXTRA LIFE';
                break;
        }
    }

    update(deltaTime) {
        if (!this.isAlive) return;
        
        // Update age
        this.age += deltaTime;
        
        // Check for expiration
        if (this.age >= this.lifetime) {
            this.destroy();
            return;
        }
        
        // Start blinking when near expiration
        if (this.age > this.lifetime * 0.8) {
            this.isBlinking = true;
            this.blinkTimer += deltaTime;
        }
        
        // Update movement
        this.updateMovement(deltaTime);
        
        // Update visual effects
        this.updateEffects(deltaTime);
        
        // Check for magnetic attraction to player
        this.updateMagneticAttraction(deltaTime);
    }

    updateMovement(deltaTime) {
        // Basic leftward movement
        this.position.x += this.velocity.x * deltaTime * 0.1;
        this.position.y += this.velocity.y * deltaTime * 0.1;
        
        // Add slight bobbing motion
        this.bobOffset += this.bobSpeed * deltaTime;
        const bobY = Math.sin(this.bobOffset) * this.bobAmplitude;
        this.position.y += bobY * deltaTime * 0.01;
    }

    updateEffects(deltaTime) {
        // Update rotation
        this.rotationAngle += deltaTime * 0.002;
        
        // Update glow intensity
        this.glowIntensity = (Math.sin(Date.now() * 0.005) + 1) * 0.5;
        
        // Create sparkle particles
        if (Math.random() < 0.1) {
            this.createSparkleParticle();
        }
    }

    updateMagneticAttraction(deltaTime) {
        if (!window.gameEngine || !window.gameEngine.player) return;
        
        const player = window.gameEngine.player;
        const distance = this.position.distance(player.position);
        
        if (distance < this.magneticRange) {
            const direction = player.position.subtract(this.position).normalize();
            const attractionForce = this.magneticStrength * (1 - distance / this.magneticRange);
            
            this.velocity.x += direction.x * attractionForce;
            this.velocity.y += direction.y * attractionForce;
            
            // Limit velocity
            const maxVelocity = 3;
            if (this.velocity.magnitude() > maxVelocity) {
                this.velocity = this.velocity.normalize().multiply(maxVelocity);
            }
        }
    }

    createSparkleParticle() {
        if (!window.particleSystem) return;
        
        const sparkleX = this.position.x + Utils.random(-8, 8);
        const sparkleY = this.position.y + Utils.random(-8, 8);
        
        const particle = new Particle(sparkleX, sparkleY, 0, 0, {
            life: 0.5,
            decay: 0.05,
            size: Utils.random(1, 2),
            color: this.color,
            glow: true,
            scaleOverTime: true
        });
        
        window.particleSystem.addParticle(particle);
    }

    destroy() {
        this.isAlive = false;
        
        // Create collection effect
        if (window.particleSystem) {
            window.particleSystem.createPowerUpEffect(
                this.position.x,
                this.position.y,
                this.color
            );
        }
    }

    render(ctx) {
        if (!this.isAlive) return;
        
        // Skip rendering if blinking
        if (this.isBlinking && Math.floor(this.blinkTimer / 100) % 2) {
            return;
        }
        
        ctx.save();
        
        // Apply rotation
        ctx.translate(this.position.x, this.position.y);
        ctx.rotate(this.rotationAngle);
        
        // Render glow effect
        const glowSize = this.size.x + this.glowIntensity * 8;
        ctx.fillStyle = this.color + '40'; // Semi-transparent
        ctx.fillRect(-glowSize / 2, -glowSize / 2, glowSize, glowSize);
        
        // Render main body
        ctx.fillStyle = this.color;
        ctx.fillRect(-this.size.x / 2, -this.size.y / 2, this.size.x, this.size.y);
        
        // Render border
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 1;
        ctx.strokeRect(-this.size.x / 2, -this.size.y / 2, this.size.x, this.size.y);
        
        // Render icon
        ctx.fillStyle = '#000000';
        ctx.font = 'bold 12px monospace';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(this.icon, 0, 0);
        
        ctx.restore();
        
        // Render description when close to player
        this.renderDescription(ctx);
    }

    renderDescription(ctx) {
        if (!window.gameEngine || !window.gameEngine.player) return;
        
        const player = window.gameEngine.player;
        const distance = this.position.distance(player.position);
        
        if (distance < 60) {
            ctx.fillStyle = '#ffffff';
            ctx.font = '10px monospace';
            ctx.textAlign = 'center';
            ctx.fillText(
                this.description,
                this.position.x,
                this.position.y - 20
            );
        }
    }
}

// Special power-up types
class WeaponPowerUp extends PowerUp {
    constructor(x, y, weaponType) {
        super(x, y, weaponType);
        this.weaponDuration = this.getWeaponDuration();
    }

    getWeaponDuration() {
        switch (this.type) {
            case 'spreadShot': return 15000;
            case 'rapidFire': return 10000;
            case 'laser': return 8000;
            case 'homingMissile': return 12000;
            default: return 10000;
        }
    }
}

class UtilityPowerUp extends PowerUp {
    constructor(x, y, utilityType) {
        super(x, y, utilityType);
        this.effectValue = this.getEffectValue();
    }

    getEffectValue() {
        switch (this.type) {
            case 'shield': return 5000; // 5 seconds of invulnerability
            case 'bomb': return 1; // Number of bombs
            case 'scoreBonus': return 1000; // Score points
            case 'extraLife': return 1; // Number of lives
            default: return 0;
        }
    }
}

// Power-up manager
class PowerUpManager {
    constructor() {
        this.powerUps = [];
        this.dropChance = GAME_CONFIG.POWERUP_DROP_CHANCE;
        this.lastDropTime = 0;
        this.minDropInterval = 3000; // Minimum 3 seconds between drops
    }

    update(deltaTime) {
        // Update all power-ups
        this.powerUps.forEach(powerUp => powerUp.update(deltaTime));
        
        // Remove expired power-ups
        this.powerUps = this.powerUps.filter(powerUp => powerUp.isAlive);
    }

    spawnPowerUp(x, y, forceType = null) {
        const currentTime = Date.now();
        
        // Prevent too frequent drops
        if (currentTime - this.lastDropTime < this.minDropInterval) {
            return null;
        }
        
        const powerUpType = forceType || this.selectRandomPowerUpType();
        let powerUp;
        
        if (['spreadShot', 'rapidFire', 'laser', 'homingMissile'].includes(powerUpType)) {
            powerUp = new WeaponPowerUp(x, y, powerUpType);
        } else {
            powerUp = new UtilityPowerUp(x, y, powerUpType);
        }
        
        this.powerUps.push(powerUp);
        this.lastDropTime = currentTime;
        
        return powerUp;
    }

    selectRandomPowerUpType() {
        const types = [
            'spreadShot', 'rapidFire', 'laser', 'homingMissile',
            'shield', 'bomb', 'scoreBonus', 'extraLife'
        ];
        
        const weights = [20, 20, 15, 15, 10, 8, 7, 5];
        
        return this.weightedRandomChoice(types, weights);
    }

    weightedRandomChoice(items, weights) {
        const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
        let random = Math.random() * totalWeight;
        
        for (let i = 0; i < items.length; i++) {
            random -= weights[i];
            if (random <= 0) {
                return items[i];
            }
        }
        
        return items[0];
    }

    collectPowerUp(powerUp, player) {
        if (!powerUp.isAlive) return false;
        
        switch (powerUp.type) {
            case 'spreadShot':
            case 'rapidFire':
            case 'laser':
            case 'homingMissile':
                player.collectPowerUp(powerUp);
                break;
                
            case 'shield':
                player.activateShield();
                break;
                
            case 'bomb':
                this.activateSmartBomb();
                break;
                
            case 'scoreBonus':
                if (window.gameEngine) {
                    window.gameEngine.score += powerUp.effectValue;
                }
                break;
                
            case 'extraLife':
                if (window.gameEngine) {
                    window.gameEngine.lives += powerUp.effectValue;
                }
                break;
        }
        
        powerUp.destroy();
        
        // Play power-up sound
        if (window.audioManager) {
            window.audioManager.playSound('powerUp');
        }
        
        return true;
    }

    activateSmartBomb() {
        if (!window.gameEngine) return;
        
        // Destroy all enemies on screen
        window.gameEngine.enemies.forEach(enemy => {
            if (enemy.isAlive) {
                enemy.destroy();
                window.gameEngine.score += enemy.scoreValue;
            }
        });
        
        // Destroy all enemy projectiles
        window.gameEngine.projectiles = window.gameEngine.projectiles.filter(projectile => {
            if (projectile.type === 'enemy') {
                projectile.destroy();
                return false;
            }
            return true;
        });
        
        // Create screen-wide explosion effect
        if (window.particleSystem) {
            for (let i = 0; i < 50; i++) {
                const x = Utils.random(0, GAME_CONFIG.CANVAS_WIDTH);
                const y = Utils.random(0, GAME_CONFIG.CANVAS_HEIGHT);
                window.particleSystem.createExplosion(x, y, {
                    count: 8,
                    colors: ['#ffffff', '#ffff00', '#ff8800'],
                    speed: 4
                });
            }
        }
        
        // Play bomb sound
        if (window.audioManager) {
            window.audioManager.playSound('bomb');
        }
    }

    clear() {
        this.powerUps = [];
    }

    render(ctx) {
        this.powerUps.forEach(powerUp => powerUp.render(ctx));
    }
}

// Global power-up manager instance
window.powerUpManager = new PowerUpManager();
