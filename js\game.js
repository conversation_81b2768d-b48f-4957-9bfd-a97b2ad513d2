// Main game file that ties everything together

class Game {
    constructor() {
        this.isInitialized = false;
        this.debugMode = false;
        
        // Game systems
        this.engine = null;
        this.inputHandler = null;
        this.audioManager = null;
        this.particleSystem = null;
        this.powerUpManager = null;
        
        // Performance monitoring
        this.performanceStats = {
            frameTime: 0,
            updateTime: 0,
            renderTime: 0,
            objectCount: 0
        };
        
        this.init();
    }

    init() {
        console.log('Initializing Space Shooter Game...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeGame());
        } else {
            this.initializeGame();
        }
    }

    initializeGame() {
        try {
            // Initialize all game systems
            this.initializeSystems();
            
            // Set up global references
            this.setupGlobalReferences();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Initialize debug mode if needed
            this.initializeDebugMode();
            
            this.isInitialized = true;
            console.log('Game initialized successfully!');
            
            // Show start screen
            this.showStartScreen();
            
        } catch (error) {
            console.error('Failed to initialize game:', error);
            this.showErrorMessage('Failed to initialize game. Please refresh the page.');
        }
    }

    initializeSystems() {
        // Game engine should already be created by gameEngine.js
        this.engine = window.gameEngine;
        if (!this.engine) {
            throw new Error('Game engine not found');
        }
        
        // Input handler should already be created by input.js
        this.inputHandler = window.inputHandler;
        if (!this.inputHandler) {
            throw new Error('Input handler not found');
        }
        
        // Audio manager should already be created by audio.js
        this.audioManager = window.audioManager;
        if (!this.audioManager) {
            console.warn('Audio manager not found - audio will be disabled');
        }
        
        // Particle system should already be created by particles.js
        this.particleSystem = window.particleSystem;
        if (!this.particleSystem) {
            console.warn('Particle system not found - visual effects will be limited');
        }
        
        // Power-up manager should already be created by powerUps.js
        this.powerUpManager = window.powerUpManager;
        if (!this.powerUpManager) {
            console.warn('Power-up manager not found - power-ups will be disabled');
        }
    }

    setupGlobalReferences() {
        // Make sure all systems can access each other
        window.game = this;
        
        // Set up cross-references
        if (this.engine) {
            this.engine.particleSystem = this.particleSystem;
            this.engine.powerUpManager = this.powerUpManager;
        }
    }

    setupEventListeners() {
        // Window events
        window.addEventListener('resize', () => this.handleResize());
        window.addEventListener('beforeunload', () => this.cleanup());
        
        // Visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.engine && this.engine.gameState === 'playing') {
                this.engine.pauseGame();
            }
        });
        
        // Error handling
        window.addEventListener('error', (event) => {
            console.error('Game error:', event.error);
            this.handleGameError(event.error);
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            this.handleGlobalKeydown(event);
        });
    }

    handleGlobalKeydown(event) {
        // Global keyboard shortcuts
        switch (event.code) {
            case 'F1':
                event.preventDefault();
                this.toggleDebugMode();
                break;
            case 'F2':
                event.preventDefault();
                this.toggleFullscreen();
                break;
            case 'F3':
                event.preventDefault();
                if (this.audioManager) {
                    this.audioManager.toggleSound();
                }
                break;
            case 'F4':
                event.preventDefault();
                if (this.audioManager) {
                    this.audioManager.toggleMusic();
                }
                break;
        }
    }

    initializeDebugMode() {
        // Check for debug mode in URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('debug') === 'true') {
            this.enableDebugMode();
        }
    }

    enableDebugMode() {
        this.debugMode = true;
        window.DEBUG = true;
        
        // Add debug UI
        this.createDebugUI();
        
        console.log('Debug mode enabled');
        console.log('Controls:');
        console.log('F1 - Toggle debug mode');
        console.log('F2 - Toggle fullscreen');
        console.log('F3 - Toggle sound');
        console.log('F4 - Toggle music');
    }

    createDebugUI() {
        const debugPanel = document.createElement('div');
        debugPanel.id = 'debugPanel';
        debugPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #333;
            border-radius: 5px;
            z-index: 1000;
            min-width: 200px;
        `;
        
        document.body.appendChild(debugPanel);
        
        // Update debug info periodically
        setInterval(() => this.updateDebugInfo(), 100);
    }

    updateDebugInfo() {
        const debugPanel = document.getElementById('debugPanel');
        if (!debugPanel || !this.engine) return;
        
        const stats = this.performanceStats;
        const engine = this.engine;
        
        debugPanel.innerHTML = `
            <div><strong>DEBUG INFO</strong></div>
            <div>FPS: ${engine.fps}</div>
            <div>Frame Time: ${stats.frameTime.toFixed(2)}ms</div>
            <div>Objects: ${stats.objectCount}</div>
            <div>Enemies: ${engine.enemies.length}</div>
            <div>Projectiles: ${engine.projectiles.length}</div>
            <div>Particles: ${this.particleSystem ? this.particleSystem.getParticleCount() : 0}</div>
            <div>Power-ups: ${engine.powerUps.length}</div>
            <div>Stage: ${engine.currentStage}</div>
            <div>Score: ${engine.score}</div>
            <div>Lives: ${engine.lives}</div>
            <div>Game State: ${engine.gameState}</div>
        `;
    }

    toggleDebugMode() {
        if (this.debugMode) {
            this.disableDebugMode();
        } else {
            this.enableDebugMode();
        }
    }

    disableDebugMode() {
        this.debugMode = false;
        window.DEBUG = false;
        
        const debugPanel = document.getElementById('debugPanel');
        if (debugPanel) {
            debugPanel.remove();
        }
        
        console.log('Debug mode disabled');
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.warn('Could not enter fullscreen:', err);
            });
        } else {
            document.exitFullscreen().catch(err => {
                console.warn('Could not exit fullscreen:', err);
            });
        }
    }

    handleResize() {
        // Handle window resize
        if (this.engine && this.engine.canvas) {
            // Maintain aspect ratio
            const container = document.getElementById('gameContainer');
            if (container) {
                const containerRect = container.getBoundingClientRect();
                const aspectRatio = GAME_CONFIG.CANVAS_WIDTH / GAME_CONFIG.CANVAS_HEIGHT;
                
                let newWidth = containerRect.width;
                let newHeight = containerRect.width / aspectRatio;
                
                if (newHeight > containerRect.height) {
                    newHeight = containerRect.height;
                    newWidth = containerRect.height * aspectRatio;
                }
                
                this.engine.canvas.style.width = newWidth + 'px';
                this.engine.canvas.style.height = newHeight + 'px';
            }
        }
    }

    handleGameError(error) {
        console.error('Game error occurred:', error);
        
        // Try to recover gracefully
        if (this.engine) {
            if (this.engine.gameState === 'playing') {
                this.engine.pauseGame();
            }
        }
        
        // Show error message to user
        this.showErrorMessage('A game error occurred. The game has been paused.');
    }

    showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Orbitron', monospace;
            text-align: center;
            z-index: 10000;
            max-width: 400px;
        `;
        
        errorDiv.innerHTML = `
            <h3>Error</h3>
            <p>${message}</p>
            <button onclick="this.parentElement.remove()" style="
                background: #ff0080;
                border: none;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin-top: 10px;
            ">OK</button>
        `;
        
        document.body.appendChild(errorDiv);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 10000);
    }

    showStartScreen() {
        // Make sure start screen is visible
        const startScreen = document.getElementById('startScreen');
        if (startScreen) {
            startScreen.classList.remove('hidden');
        }
        
        // Hide other screens
        const screens = ['pauseScreen', 'gameOverScreen', 'stageCompleteScreen'];
        screens.forEach(screenId => {
            const screen = document.getElementById(screenId);
            if (screen) {
                screen.classList.add('hidden');
            }
        });
    }

    // Game state management
    startNewGame() {
        if (this.engine) {
            this.engine.startGame();
        }
    }

    pauseGame() {
        if (this.engine) {
            this.engine.pauseGame();
        }
    }

    resumeGame() {
        if (this.engine) {
            this.engine.resumeGame();
        }
    }

    restartGame() {
        if (this.engine) {
            this.engine.restartGame();
        }
    }

    // Performance monitoring
    updatePerformanceStats() {
        if (!this.engine) return;
        
        this.performanceStats.objectCount = 
            this.engine.enemies.length +
            this.engine.projectiles.length +
            this.engine.powerUps.length +
            this.engine.bosses.length +
            (this.particleSystem ? this.particleSystem.getParticleCount() : 0);
    }

    // Cleanup
    cleanup() {
        console.log('Cleaning up game...');
        
        // Stop any running timers or intervals
        if (this.engine) {
            this.engine.isRunning = false;
        }
        
        // Stop audio
        if (this.audioManager) {
            this.audioManager.stopMusic();
        }
        
        // Clear any remaining timeouts/intervals
        // (This would be expanded based on what timers are used)
    }

    // Utility methods
    getGameState() {
        return this.engine ? this.engine.gameState : 'unknown';
    }

    getScore() {
        return this.engine ? this.engine.score : 0;
    }

    getLives() {
        return this.engine ? this.engine.lives : 0;
    }

    getCurrentStage() {
        return this.engine ? this.engine.currentStage : 1;
    }
}

// Initialize the game when the script loads
document.addEventListener('DOMContentLoaded', () => {
    window.spaceShooterGame = new Game();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Game;
}
