<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Space Shooter - Classic Arcade Action</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="gameContainer">
        <div id="gameUI">
            <div id="topUI">
                <div id="scoreDisplay">
                    <span class="label">SCORE</span>
                    <span id="score">000000</span>
                </div>
                <div id="livesDisplay">
                    <span class="label">LIVES</span>
                    <div id="livesContainer">
                        <span class="life">♦</span>
                        <span class="life">♦</span>
                        <span class="life">♦</span>
                    </div>
                </div>
                <div id="stageDisplay">
                    <span class="label">STAGE</span>
                    <span id="currentStage">1</span>
                </div>
            </div>
            <div id="powerUpIndicator">
                <span class="label">WEAPON</span>
                <span id="currentWeapon">BASIC</span>
            </div>

            <div id="comboIndicator">
                <span class="label">COMBO</span>
                <span id="currentCombo">0</span>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="1200" height="800"></canvas>
        
        <div id="gameOverlay">
            <div id="startScreen" class="screen">
                <h1>SPACE SHOOTER</h1>
                <h2>CLASSIC ARCADE ACTION</h2>
                <div class="instructions">
                    <p>ARROW KEYS - Move Ship</p>
                    <p>SPACEBAR - Fire Weapons</p>
                    <p>ESC - Pause Game</p>
                </div>
                <button id="startButton" class="game-button">START GAME</button>
            </div>
            
            <div id="pauseScreen" class="screen hidden">
                <h2>GAME PAUSED</h2>
                <button id="resumeButton" class="game-button">RESUME</button>
                <button id="restartButton" class="game-button">RESTART</button>
            </div>
            
            <div id="gameOverScreen" class="screen hidden">
                <h2>GAME OVER</h2>
                <div id="finalScore">
                    <span class="label">FINAL SCORE</span>
                    <span id="finalScoreValue">000000</span>
                </div>
                <button id="playAgainButton" class="game-button">PLAY AGAIN</button>
            </div>
            
            <div id="stageCompleteScreen" class="screen hidden">
                <h2>STAGE COMPLETE</h2>
                <div id="stageBonus">
                    <span class="label">STAGE BONUS</span>
                    <span id="stageBonusValue">0000</span>
                </div>
                <button id="nextStageButton" class="game-button">NEXT STAGE</button>
            </div>
        </div>
    </div>

    <!-- Audio elements for sound effects -->
    <audio id="shootSound" preload="auto">
        <source src="sounds/shoot.wav" type="audio/wav">
    </audio>
    <audio id="explosionSound" preload="auto">
        <source src="sounds/explosion.wav" type="audio/wav">
    </audio>
    <audio id="powerUpSound" preload="auto">
        <source src="sounds/powerup.wav" type="audio/wav">
    </audio>
    <audio id="backgroundMusic" preload="auto" loop>
        <source src="sounds/background.mp3" type="audio/mpeg">
    </audio>

    <!-- Game Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/gameEngine.js"></script>
    <script src="js/player.js"></script>
    <script src="js/enemies.js"></script>
    <script src="js/projectiles.js"></script>
    <script src="js/powerUps.js"></script>
    <script src="js/stages.js"></script>
    <script src="js/bosses.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/audio.js"></script>
    <script src="js/input.js"></script>
    <script src="js/game.js"></script>
</body>
</html>
