/* Space Shooter - Retro Arcade Styling */

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e);
    font-family: 'Orbitron', monospace;
    color: #00ffff;
    overflow: hidden;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

#gameContainer {
    position: relative;
    width: 1200px;
    height: 800px;
    border: 3px solid #00ffff;
    border-radius: 10px;
    box-shadow: 
        0 0 20px #00ffff,
        inset 0 0 20px rgba(0, 255, 255, 0.1);
    background: radial-gradient(ellipse at center, #0f0f23 0%, #000000 100%);
}

#gameCanvas {
    display: block;
    background: transparent;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

/* UI Styling */
#gameUI {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    pointer-events: none;
}

#topUI {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, transparent 100%);
}

#scoreDisplay, #livesDisplay, #stageDisplay {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.label {
    font-size: 12px;
    font-weight: 700;
    color: #ffff00;
    text-shadow: 0 0 5px #ffff00;
    letter-spacing: 2px;
}

#score, #currentStage {
    font-size: 24px;
    font-weight: 900;
    color: #00ffff;
    text-shadow: 0 0 10px #00ffff;
    letter-spacing: 1px;
}

#livesContainer {
    display: flex;
    gap: 8px;
}

.life {
    font-size: 20px;
    color: #ff0080;
    text-shadow: 0 0 8px #ff0080;
    animation: pulse 2s infinite;
}

.life.lost {
    color: #333;
    text-shadow: none;
    animation: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

#powerUpIndicator {
    position: absolute;
    top: 80px;
    left: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

#comboIndicator {
    position: absolute;
    top: 140px;
    left: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

#currentWeapon {
    font-size: 16px;
    font-weight: 700;
    color: #00ff00;
    text-shadow: 0 0 8px #00ff00;
    padding: 5px 10px;
    border: 1px solid #00ff00;
    border-radius: 5px;
    background: rgba(0, 255, 0, 0.1);
}

#currentCombo {
    font-size: 16px;
    font-weight: 700;
    color: #ffff00;
    text-shadow: 0 0 8px #ffff00;
    padding: 5px 10px;
    border: 1px solid #ffff00;
    border-radius: 5px;
    background: rgba(255, 255, 0, 0.1);
}

/* Game Overlay Screens */
#gameOverlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    display: flex;
    justify-content: center;
    align-items: center;
}

.screen {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(26, 26, 46, 0.9));
    border: 2px solid #00ffff;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    box-shadow: 
        0 0 30px #00ffff,
        inset 0 0 30px rgba(0, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.screen.hidden {
    display: none;
}

.screen h1 {
    font-size: 48px;
    font-weight: 900;
    color: #ffff00;
    text-shadow: 0 0 20px #ffff00;
    margin-bottom: 10px;
    letter-spacing: 3px;
}

.screen h2 {
    font-size: 32px;
    font-weight: 700;
    color: #00ffff;
    text-shadow: 0 0 15px #00ffff;
    margin-bottom: 20px;
    letter-spacing: 2px;
}

.instructions {
    margin: 30px 0;
    padding: 20px;
    border: 1px solid #666;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.05);
}

.instructions p {
    font-size: 16px;
    margin: 10px 0;
    color: #ccc;
    letter-spacing: 1px;
}

.game-button {
    background: linear-gradient(45deg, #ff0080, #ff4080);
    border: 2px solid #ff0080;
    color: white;
    font-family: 'Orbitron', monospace;
    font-size: 18px;
    font-weight: 700;
    padding: 15px 30px;
    margin: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 2px;
    box-shadow: 0 0 15px rgba(255, 0, 128, 0.5);
}

.game-button:hover {
    background: linear-gradient(45deg, #ff4080, #ff0080);
    box-shadow: 0 0 25px rgba(255, 0, 128, 0.8);
    transform: translateY(-2px);
}

.game-button:active {
    transform: translateY(0);
    box-shadow: 0 0 10px rgba(255, 0, 128, 0.6);
}

#finalScore, #stageBonus {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #00ffff;
    border-radius: 8px;
    background: rgba(0, 255, 255, 0.1);
}

#finalScoreValue, #stageBonusValue {
    font-size: 28px;
    font-weight: 900;
    color: #ffff00;
    text-shadow: 0 0 15px #ffff00;
    display: block;
    margin-top: 10px;
}

/* Responsive adjustments */
@media (max-width: 1250px) {
    #gameContainer {
        width: 95vw;
        height: 63.33vw;
        max-height: 95vh;
    }
    
    #gameCanvas {
        width: 100%;
        height: 100%;
    }
}

/* Loading animation */
@keyframes glow {
    0%, 100% { text-shadow: 0 0 5px currentColor; }
    50% { text-shadow: 0 0 20px currentColor, 0 0 30px currentColor; }
}

.glow {
    animation: glow 2s infinite;
}
