# Space Shooter - Classic Arcade Action

A retro-style horizontal side-scrolling space shooter game built with HTML5, CSS3, and JavaScript. Experience intense combat mechanics, progressive difficulty scaling, and detailed pixel art graphics across 5 unique stages.

## 🎮 Game Features

### Core Gameplay
- **Classic Side-Scrolling Action**: Horizontal movement through space environments
- **Lives System**: Start with 3 lives, lose one upon taking damage
- **Progressive Difficulty**: Each stage increases in complexity and challenge
- **Score System**: Earn points for destroying enemies and collecting power-ups

### Player Mechanics
- **Smooth Controls**: Responsive movement with arrow keys or WASD
- **Weapon System**: Primary weapon with multiple upgrade paths
- **Power-Up Collection**: Enhance your ship with temporary weapon upgrades
- **Collision Detection**: Precise hitboxes for fair gameplay

### Enemy Variety
- **Diagonal Attackers**: Enemies approaching at 45-degree angles
- **Zigzag Flyers**: Units moving in sharp Z-pattern trajectories
- **Circular Formation**: Enemies flying in orbital patterns
- **Straight Rushers**: Fast-moving direct assault units
- **Formation Flyers**: Coordinated group attacks
- **Heavy Units**: Armored enemies with multiple hit points

### Power-Up System
- **Spread Shot**: Fires multiple bullets in a fan pattern
- **Rapid Fire**: Significantly increased shooting rate
- **Laser Beam**: Continuous damage beam with limited duration
- **Homing Missiles**: Projectiles that track nearby enemies
- **Shield Generator**: Temporary invincibility
- **Smart Bombs**: Screen-clearing emergency weapons
- **Score Bonuses**: Extra points for high scores
- **Extra Lives**: Additional chances to continue

### Stage Progression

#### Stage 1: Asteroid Field
- **Environment**: Rocky asteroid formations with debris
- **Enemies**: Basic fighters with simple movement patterns
- **Challenge**: Learn controls and basic enemy patterns

#### Stage 2: Space Station Approach
- **Environment**: Industrial structures and defense platforms
- **Enemies**: Turrets and patrol ships with complex patterns
- **Challenge**: Combination of stationary and mobile threats

#### Stage 3: Nebula Combat Zone
- **Environment**: Colorful gas clouds with electrical storms
- **Enemies**: Advanced fighters with zigzag and circular movements
- **Challenge**: Visibility obstruction and unpredictable behavior

#### Stage 4: Fleet Engagement
- **Environment**: Large capital ships and fighter squadrons
- **Enemies**: Coordinated attack formations and heavy units
- **Challenge**: Multiple simultaneous threats

#### Stage 5: Core Assault
- **Environment**: Massive space fortress interior
- **Enemies**: Elite defenders with all movement patterns
- **Challenge**: Maximum difficulty with all enemy types

### Boss Battles
Each stage culminates with a unique boss encounter featuring:
- **Massive Scale**: Significantly larger than regular enemies
- **Weak Point System**: Multiple vulnerable spots to target
- **Phase Transitions**: Behavior changes as weak points are destroyed
- **Unique Attack Patterns**: Stage-specific offensive abilities
- **Progressive Difficulty**: Each boss more challenging than the last

## 🎯 Controls

### Keyboard Controls
- **Arrow Keys** or **WASD**: Move ship
- **Spacebar** or **Z**: Fire weapons
- **X**: Use smart bomb (when available)
- **Escape** or **P**: Pause game
- **F1**: Toggle debug mode
- **F2**: Toggle fullscreen
- **F3**: Toggle sound effects
- **F4**: Toggle background music

### Gamepad Support
- **Left Stick/D-Pad**: Move ship
- **A/X/Right Bumper**: Fire weapons
- **B/Y**: Use smart bomb
- **Start**: Pause game

### Touch Controls (Mobile)
- **Virtual D-Pad**: Ship movement
- **Fire Button**: Shoot weapons
- **Bomb Button**: Smart bomb

## 🚀 Getting Started

### Prerequisites
- Modern web browser with HTML5 support
- JavaScript enabled
- Audio support for sound effects and music

### Installation
1. Download or clone the game files
2. Open `index.html` in your web browser
3. Click "START GAME" to begin playing

### File Structure
```
SpaceShooter/
├── index.html          # Main game page
├── styles.css          # Game styling and UI
├── README.md           # This file
└── js/
    ├── utils.js        # Utility functions and constants
    ├── gameEngine.js   # Core game engine and loop
    ├── player.js       # Player ship implementation
    ├── enemies.js      # Enemy system and AI
    ├── projectiles.js  # Bullet and missile physics
    ├── powerUps.js     # Power-up system
    ├── particles.js    # Visual effects system
    ├── stages.js       # Stage progression and environments
    ├── bosses.js       # Boss battle mechanics
    ├── audio.js        # Sound effects and music
    ├── input.js        # Input handling system
    └── game.js         # Main game coordination
```

## 🎨 Technical Features

### Graphics
- **Pixel Art Style**: Retro 16-bit inspired visuals
- **Smooth Animations**: 60 FPS gameplay
- **Particle Effects**: Explosions, sparks, and trails
- **Parallax Scrolling**: Multi-layered background movement
- **Visual Feedback**: Hit flashes, damage numbers, and effects

### Audio
- **Dynamic Sound Effects**: Procedurally generated using Web Audio API
- **Spatial Audio**: Positional sound effects
- **Background Music**: Stage-appropriate soundtracks
- **Audio Controls**: Individual volume controls for effects and music

### Performance
- **Optimized Rendering**: Efficient sprite drawing and collision detection
- **Object Pooling**: Reuse of game objects to reduce garbage collection
- **Adaptive Quality**: Performance monitoring and adjustment
- **Memory Management**: Automatic cleanup of off-screen objects

### Accessibility
- **Keyboard Navigation**: Full game playable with keyboard
- **Gamepad Support**: Xbox and PlayStation controller compatibility
- **Touch Controls**: Mobile-friendly virtual controls
- **Visual Indicators**: Clear UI feedback for all actions

## 🔧 Configuration

### Debug Mode
Enable debug mode by adding `?debug=true` to the URL or pressing F1:
- Performance statistics display
- Object count monitoring
- Frame rate information
- Game state debugging

### Customization
Modify `js/utils.js` to adjust:
- Game difficulty settings
- Player movement speed
- Enemy spawn rates
- Power-up drop chances
- Visual effect intensity

## 🎯 Scoring System

### Points Awarded
- **Basic Enemy**: 100 points
- **Fast Enemy**: 150 points
- **Zigzag Enemy**: 200 points
- **Circular Enemy**: 250 points
- **Shooter Enemy**: 300 points
- **Heavy Enemy**: 500 points
- **Boss Weak Point**: 1000 points
- **Boss Defeat**: 5000 points
- **Power-Up Collection**: 100 points
- **Stage Completion**: 1000 × Stage Number

### Bonus Features
- **Combo System**: Consecutive enemy eliminations increase score multiplier
- **Stage Bonus**: Additional points for completing stages quickly
- **Accuracy Bonus**: Extra points for high hit percentage

## 🐛 Troubleshooting

### Common Issues
- **No Sound**: Check browser audio permissions and volume settings
- **Poor Performance**: Close other browser tabs and applications
- **Controls Not Working**: Ensure the game window has focus
- **Visual Glitches**: Try refreshing the page or updating your browser

### Browser Compatibility
- **Chrome**: Fully supported
- **Firefox**: Fully supported
- **Safari**: Supported with minor audio limitations
- **Edge**: Fully supported
- **Mobile Browsers**: Basic support with touch controls

## 🏆 Tips and Strategies

### Survival Tips
- Keep moving to avoid enemy fire
- Prioritize power-ups for weapon upgrades
- Learn enemy movement patterns
- Use smart bombs in emergency situations
- Focus on boss weak points for maximum damage

### High Score Strategies
- Maintain combo chains by destroying enemies quickly
- Collect all power-ups for bonus points
- Complete stages as fast as possible for time bonuses
- Destroy all enemies in formation attacks
- Master boss patterns for efficient defeats

## 📝 Credits

### Development
- **Game Design**: Classic arcade shooter mechanics
- **Programming**: HTML5, CSS3, JavaScript (ES6+)
- **Graphics**: Pixel art style rendering
- **Audio**: Web Audio API procedural generation
- **Testing**: Cross-browser compatibility

### Inspiration
- Classic arcade shooters like Gradius and R-Type
- Modern indie games with retro aesthetics
- Traditional side-scrolling action games

## 📄 License

This project is open source and available under the MIT License. Feel free to modify, distribute, and use the code for educational or commercial purposes.

## 🚀 Future Enhancements

### Planned Features
- **Multiplayer Mode**: Cooperative and competitive gameplay
- **Level Editor**: Create custom stages and share with others
- **Achievement System**: Unlock rewards for specific accomplishments
- **Leaderboards**: Online high score tracking
- **Additional Stages**: More environments and challenges
- **New Weapons**: Expanded arsenal of upgrades
- **Story Mode**: Narrative campaign with cutscenes

---

**Enjoy the classic arcade action of Space Shooter!** 🚀✨
