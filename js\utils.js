// Utility functions for the Space Shooter game

class Vector2 {
    constructor(x = 0, y = 0) {
        this.x = x;
        this.y = y;
    }

    add(vector) {
        return new Vector2(this.x + vector.x, this.y + vector.y);
    }

    subtract(vector) {
        return new Vector2(this.x - vector.x, this.y - vector.y);
    }

    multiply(scalar) {
        return new Vector2(this.x * scalar, this.y * scalar);
    }

    magnitude() {
        return Math.sqrt(this.x * this.x + this.y * this.y);
    }

    normalize() {
        const mag = this.magnitude();
        if (mag === 0) return new Vector2(0, 0);
        return new Vector2(this.x / mag, this.y / mag);
    }

    distance(vector) {
        return this.subtract(vector).magnitude();
    }

    copy() {
        return new Vector2(this.x, this.y);
    }
}

class Rectangle {
    constructor(x, y, width, height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }

    intersects(other) {
        return this.x < other.x + other.width &&
               this.x + this.width > other.x &&
               this.y < other.y + other.height &&
               this.y + this.height > other.y;
    }

    contains(point) {
        return point.x >= this.x &&
               point.x <= this.x + this.width &&
               point.y >= this.y &&
               point.y <= this.y + this.height;
    }

    center() {
        return new Vector2(
            this.x + this.width / 2,
            this.y + this.height / 2
        );
    }
}

// Utility functions
const Utils = {
    // Clamp a value between min and max
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },

    // Linear interpolation
    lerp(start, end, factor) {
        return start + (end - start) * factor;
    },

    // Random number between min and max
    random(min, max) {
        return Math.random() * (max - min) + min;
    },

    // Random integer between min and max (inclusive)
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },

    // Convert degrees to radians
    toRadians(degrees) {
        return degrees * Math.PI / 180;
    },

    // Convert radians to degrees
    toDegrees(radians) {
        return radians * 180 / Math.PI;
    },

    // Calculate angle between two points
    angleBetween(point1, point2) {
        return Math.atan2(point2.y - point1.y, point2.x - point1.x);
    },

    // Check if a point is within screen bounds
    isInBounds(point, margin = 0) {
        return point.x >= -margin &&
               point.x <= GAME_CONFIG.CANVAS_WIDTH + margin &&
               point.y >= -margin &&
               point.y <= GAME_CONFIG.CANVAS_HEIGHT + margin;
    },

    // Create a simple easing function
    easeInOut(t) {
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    },

    // Format score with leading zeros
    formatScore(score, digits = 6) {
        return score.toString().padStart(digits, '0');
    },

    // Create explosion particles
    createExplosion(x, y, particleCount = 10, color = '#ff4400') {
        const particles = [];
        for (let i = 0; i < particleCount; i++) {
            particles.push({
                x: x,
                y: y,
                vx: Utils.random(-3, 3),
                vy: Utils.random(-3, 3),
                life: 1.0,
                decay: Utils.random(0.02, 0.05),
                size: Utils.random(2, 6),
                color: color
            });
        }
        return particles;
    },

    // Simple collision detection between circles
    circleCollision(obj1, obj2) {
        // Handle objects that use position property vs direct x/y
        const x1 = obj1.position ? obj1.position.x : obj1.x;
        const y1 = obj1.position ? obj1.position.y : obj1.y;
        const x2 = obj2.position ? obj2.position.x : obj2.x;
        const y2 = obj2.position ? obj2.position.y : obj2.y;

        const dx = x1 - x2;
        const dy = y1 - y2;
        const distance = Math.sqrt(dx * dx + dy * dy);
        return distance < (obj1.radius + obj2.radius);
    },

    // Rectangle collision detection
    rectCollision(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    },

    // Get random element from array
    randomChoice(array) {
        return array[Math.floor(Math.random() * array.length)];
    },

    // Shuffle array in place
    shuffle(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }
};

// Game configuration constants
const GAME_CONFIG = {
    CANVAS_WIDTH: 1200,
    CANVAS_HEIGHT: 800,
    FPS: 60,
    PLAYER_SPEED: 5,
    BULLET_SPEED: 8,
    ENEMY_SPAWN_RATE: 0.02,
    POWERUP_DROP_CHANCE: 0.15,
    MAX_PARTICLES: 200,
    SCROLL_SPEED: 2
};

// Color palette for the game
const COLORS = {
    PLAYER: '#00ffff',
    ENEMY: '#ff4400',
    BULLET: '#ffff00',
    ENEMY_BULLET: '#ff0080',
    POWERUP: '#00ff00',
    EXPLOSION: '#ff4400',
    BACKGROUND: '#000011',
    UI_PRIMARY: '#00ffff',
    UI_SECONDARY: '#ffff00',
    UI_DANGER: '#ff0080'
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Vector2, Rectangle, Utils, GAME_CONFIG, COLORS };
}
