// Projectile system for Space Shooter

class Projectile {
    constructor(x, y, velocityX, velocityY, type = 'player', damage = 1) {
        this.position = new Vector2(x, y);
        this.velocity = new Vector2(velocityX, velocityY);
        this.type = type; // 'player' or 'enemy'
        this.damage = damage;
        this.radius = 3;
        this.isAlive = true;
        
        // Visual properties
        this.size = new Vector2(6, 2);
        this.color = type === 'player' ? COLORS.BULLET : COLORS.ENEMY_BULLET;
        this.trail = [];
        this.maxTrailLength = 5;
        
        // Lifetime
        this.lifetime = 3000; // 3 seconds
        this.age = 0;
    }

    update(deltaTime) {
        if (!this.isAlive) return;
        
        // Update age
        this.age += deltaTime;
        if (this.age >= this.lifetime) {
            this.destroy();
            return;
        }
        
        // Store previous position for trail
        this.trail.push(this.position.copy());
        if (this.trail.length > this.maxTrailLength) {
            this.trail.shift();
        }
        
        // Update position
        this.position.x += this.velocity.x * deltaTime * 0.1;
        this.position.y += this.velocity.y * deltaTime * 0.1;
    }

    destroy() {
        this.isAlive = false;
    }

    render(ctx) {
        if (!this.isAlive) return;
        
        // Render trail
        this.renderTrail(ctx);
        
        // Render projectile
        ctx.fillStyle = this.color;
        ctx.fillRect(
            Math.floor(this.position.x - this.size.x / 2),
            Math.floor(this.position.y - this.size.y / 2),
            this.size.x,
            this.size.y
        );
        
        // Add glow effect
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 5;
        ctx.fillRect(
            Math.floor(this.position.x - this.size.x / 2),
            Math.floor(this.position.y - this.size.y / 2),
            this.size.x,
            this.size.y
        );
        ctx.shadowBlur = 0;
    }

    renderTrail(ctx) {
        if (this.trail.length < 2) return;
        
        for (let i = 0; i < this.trail.length - 1; i++) {
            const alpha = (i + 1) / this.trail.length * 0.5;
            ctx.fillStyle = this.color + Math.floor(alpha * 255).toString(16).padStart(2, '0');
            
            const pos = this.trail[i];
            const size = (i + 1) / this.trail.length * this.size.x;
            
            ctx.fillRect(
                Math.floor(pos.x - size / 2),
                Math.floor(pos.y - 1),
                size,
                2
            );
        }
    }
}

class LaserBeam extends Projectile {
    constructor(x, y, type = 'player', damage = 2) {
        super(x, y, 0, 0, type, damage);
        this.width = GAME_CONFIG.CANVAS_WIDTH;
        this.height = 4;
        this.duration = 100; // Very short duration for continuous effect
        this.intensity = 1.0;
        this.color = type === 'player' ? '#ff0000' : '#ff00ff';
    }

    update(deltaTime) {
        this.age += deltaTime;
        this.intensity = Math.max(0, 1 - (this.age / this.duration));
        
        if (this.age >= this.duration) {
            this.destroy();
        }
    }

    render(ctx) {
        if (!this.isAlive) return;
        
        const alpha = Math.floor(this.intensity * 255).toString(16).padStart(2, '0');
        
        // Main beam
        ctx.fillStyle = this.color + alpha;
        ctx.fillRect(this.position.x, this.position.y - this.height / 2, this.width, this.height);
        
        // Glow effect
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 10;
        ctx.fillRect(this.position.x, this.position.y - this.height / 2, this.width, this.height);
        ctx.shadowBlur = 0;
        
        // Core beam
        ctx.fillStyle = '#ffffff' + alpha;
        ctx.fillRect(this.position.x, this.position.y - 1, this.width, 2);
    }
}

class HomingMissile extends Projectile {
    constructor(x, y, type = 'player', damage = 3) {
        super(x, y, 6, 0, type, damage);
        this.target = null;
        this.turnSpeed = 0.05;
        this.maxSpeed = 8;
        this.seekRange = 200;
        this.thrusterParticles = [];
        this.size = new Vector2(8, 4);
        this.color = type === 'player' ? '#ff8800' : '#8800ff';
    }

    update(deltaTime) {
        if (!this.isAlive) return;
        
        // Find target
        this.findTarget();
        
        // Home in on target
        if (this.target) {
            this.homeToTarget(deltaTime);
        }
        
        // Update position
        this.position.x += this.velocity.x * deltaTime * 0.1;
        this.position.y += this.velocity.y * deltaTime * 0.1;
        
        // Create thruster particles
        this.createThrusterParticles();
        
        // Update particles
        this.updateThrusterParticles(deltaTime);
        
        // Update age
        this.age += deltaTime;
        if (this.age >= this.lifetime) {
            this.destroy();
        }
    }

    findTarget() {
        if (!window.gameEngine) return;
        
        let closestDistance = this.seekRange;
        this.target = null;
        
        const targets = this.type === 'player' ? 
            window.gameEngine.enemies : 
            [window.gameEngine.player].filter(p => p && p.isAlive);
        
        targets.forEach(target => {
            if (!target || !target.isAlive) return;
            
            const distance = this.position.distance(target.position);
            if (distance < closestDistance) {
                closestDistance = distance;
                this.target = target;
            }
        });
    }

    homeToTarget(deltaTime) {
        if (!this.target) return;
        
        const targetDirection = this.target.position.subtract(this.position).normalize();
        const currentDirection = this.velocity.normalize();
        
        // Gradually turn towards target
        this.velocity.x = Utils.lerp(currentDirection.x, targetDirection.x, this.turnSpeed) * this.maxSpeed;
        this.velocity.y = Utils.lerp(currentDirection.y, targetDirection.y, this.turnSpeed) * this.maxSpeed;
    }

    createThrusterParticles() {
        const particleX = this.position.x - 8;
        const particleY = this.position.y + Utils.random(-2, 2);
        
        this.thrusterParticles.push({
            x: particleX,
            y: particleY,
            vx: Utils.random(-3, -1),
            vy: Utils.random(-1, 1),
            life: 1.0,
            decay: 0.08,
            size: Utils.random(1, 2),
            color: '#ff4400'
        });
        
        // Limit particle count
        if (this.thrusterParticles.length > 10) {
            this.thrusterParticles.shift();
        }
    }

    updateThrusterParticles(deltaTime) {
        this.thrusterParticles.forEach(particle => {
            particle.x += particle.vx * deltaTime * 0.1;
            particle.y += particle.vy * deltaTime * 0.1;
            particle.life -= particle.decay;
        });
        
        this.thrusterParticles = this.thrusterParticles.filter(particle => particle.life > 0);
    }

    render(ctx) {
        if (!this.isAlive) return;
        
        // Render thruster particles
        this.thrusterParticles.forEach(particle => {
            ctx.fillStyle = particle.color;
            ctx.globalAlpha = particle.life;
            ctx.fillRect(particle.x, particle.y, particle.size, particle.size);
        });
        ctx.globalAlpha = 1;
        
        // Render missile body
        ctx.fillStyle = this.color;
        ctx.fillRect(
            Math.floor(this.position.x - this.size.x / 2),
            Math.floor(this.position.y - this.size.y / 2),
            this.size.x,
            this.size.y
        );
        
        // Render missile tip
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(
            Math.floor(this.position.x + this.size.x / 2 - 2),
            Math.floor(this.position.y - 1),
            2,
            2
        );
        
        // Glow effect
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 8;
        ctx.fillRect(
            Math.floor(this.position.x - this.size.x / 2),
            Math.floor(this.position.y - this.size.y / 2),
            this.size.x,
            this.size.y
        );
        ctx.shadowBlur = 0;
    }
}

class EnemyProjectile extends Projectile {
    constructor(x, y, velocityX, velocityY, damage = 1, pattern = 'straight') {
        super(x, y, velocityX, velocityY, 'enemy', damage);
        this.pattern = pattern;
        this.patternTimer = 0;
        this.amplitude = 50;
        this.frequency = 0.005;
        this.initialY = y;
        this.color = COLORS.ENEMY_BULLET;
        this.size = new Vector2(4, 4);
    }

    update(deltaTime) {
        if (!this.isAlive) return;
        
        this.patternTimer += deltaTime;
        
        // Apply movement pattern
        switch (this.pattern) {
            case 'sine':
                this.position.y = this.initialY + Math.sin(this.patternTimer * this.frequency) * this.amplitude;
                break;
            case 'spiral':
                const radius = this.patternTimer * 0.1;
                this.position.y += Math.sin(this.patternTimer * 0.01) * 2;
                break;
        }
        
        // Standard update
        super.update(deltaTime);
    }

    render(ctx) {
        if (!this.isAlive) return;
        
        // Render as a diamond shape
        ctx.fillStyle = this.color;
        ctx.save();
        ctx.translate(this.position.x, this.position.y);
        ctx.rotate(Math.PI / 4);
        ctx.fillRect(-this.size.x / 2, -this.size.y / 2, this.size.x, this.size.y);
        ctx.restore();
        
        // Add glow
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 6;
        ctx.save();
        ctx.translate(this.position.x, this.position.y);
        ctx.rotate(Math.PI / 4);
        ctx.fillRect(-this.size.x / 2, -this.size.y / 2, this.size.x, this.size.y);
        ctx.restore();
        ctx.shadowBlur = 0;
    }
}
