// Stage system for Space Shooter with 5 distinct stages

class Stage {
    constructor(stageNumber) {
        this.stageNumber = stageNumber;
        this.name = '';
        this.description = '';
        this.backgroundColor = '#000011';
        this.backgroundLayers = [];
        
        // Stage progression
        this.duration = 60000; // 60 seconds base duration
        this.enemyWaves = [];
        this.currentWave = 0;
        this.waveTimer = 0;
        this.isComplete = false;
        this.hasBoss = true;
        this.bossSpawned = false;
        
        // Environmental effects
        this.environmentalHazards = [];
        this.particleEffects = [];
        
        // Difficulty scaling
        this.difficultyMultiplier = 1 + (stageNumber - 1) * 0.3;
        
        this.initializeStage();
    }

    initializeStage() {
        switch (this.stageNumber) {
            case 1:
                this.initializeAsteroidField();
                break;
            case 2:
                this.initializeSpaceStation();
                break;
            case 3:
                this.initializeNebula();
                break;
            case 4:
                this.initializeFleetEngagement();
                break;
            case 5:
                this.initializeCoreAssault();
                break;
        }
    }

    initializeAsteroidField() {
        this.name = 'ASTEROID FIELD';
        this.description = 'Navigate through the rocky debris';
        this.backgroundColor = '#001122';
        this.duration = 45000;
        
        // Create asteroid background
        this.createAsteroidBackground();
        
        // Define enemy waves
        this.enemyWaves = [
            { time: 0, enemies: [{ type: 'basic', count: 3, interval: 1000 }] },
            { time: 8000, enemies: [{ type: 'basic', count: 2, interval: 800 }, { type: 'fast', count: 1, interval: 1500 }] },
            { time: 16000, enemies: [{ type: 'zigzag', count: 2, interval: 1200 }] },
            { time: 24000, enemies: [{ type: 'basic', count: 4, interval: 600 }] },
            { time: 32000, enemies: [{ type: 'fast', count: 3, interval: 1000 }] }
        ];
    }

    initializeSpaceStation() {
        this.name = 'SPACE STATION APPROACH';
        this.description = 'Assault the orbital defense platform';
        this.backgroundColor = '#112200';
        this.duration = 50000;
        
        // Create space station background
        this.createSpaceStationBackground();
        
        this.enemyWaves = [
            { time: 0, enemies: [{ type: 'basic', count: 2, interval: 1200 }, { type: 'shooter', count: 1, interval: 2000 }] },
            { time: 10000, enemies: [{ type: 'fast', count: 3, interval: 800 }] },
            { time: 18000, enemies: [{ type: 'zigzag', count: 2, interval: 1000 }, { type: 'shooter', count: 1, interval: 1500 }] },
            { time: 26000, enemies: [{ type: 'circular', count: 2, interval: 1500 }] },
            { time: 34000, enemies: [{ type: 'shooter', count: 3, interval: 1000 }] },
            { time: 42000, enemies: [{ type: 'formation', count: 5, interval: 500 }] }
        ];
    }

    initializeNebula() {
        this.name = 'NEBULA COMBAT ZONE';
        this.description = 'Fight through the cosmic storm';
        this.backgroundColor = '#220011';
        this.duration = 55000;
        
        // Create nebula background with particle effects
        this.createNebulaBackground();
        
        this.enemyWaves = [
            { time: 0, enemies: [{ type: 'fast', count: 4, interval: 800 }] },
            { time: 8000, enemies: [{ type: 'zigzag', count: 3, interval: 1000 }, { type: 'circular', count: 1, interval: 2000 }] },
            { time: 16000, enemies: [{ type: 'shooter', count: 2, interval: 1200 }, { type: 'basic', count: 3, interval: 800 }] },
            { time: 24000, enemies: [{ type: 'circular', count: 3, interval: 1500 }] },
            { time: 32000, enemies: [{ type: 'formation', count: 6, interval: 400 }] },
            { time: 40000, enemies: [{ type: 'heavy', count: 1, interval: 3000 }, { type: 'fast', count: 4, interval: 600 }] }
        ];
    }

    initializeFleetEngagement() {
        this.name = 'FLEET ENGAGEMENT';
        this.description = 'Battle the enemy armada';
        this.backgroundColor = '#001100';
        this.duration = 60000;
        
        // Create fleet background
        this.createFleetBackground();
        
        this.enemyWaves = [
            { time: 0, enemies: [{ type: 'formation', count: 8, interval: 300 }] },
            { time: 8000, enemies: [{ type: 'heavy', count: 2, interval: 2000 }, { type: 'shooter', count: 3, interval: 1000 }] },
            { time: 16000, enemies: [{ type: 'circular', count: 4, interval: 1200 }] },
            { time: 24000, enemies: [{ type: 'zigzag', count: 5, interval: 800 }, { type: 'fast', count: 3, interval: 600 }] },
            { time: 32000, enemies: [{ type: 'heavy', count: 3, interval: 1500 }] },
            { time: 40000, enemies: [{ type: 'formation', count: 10, interval: 200 }] },
            { time: 48000, enemies: [{ type: 'shooter', count: 6, interval: 800 }] }
        ];
    }

    initializeCoreAssault() {
        this.name = 'CORE ASSAULT';
        this.description = 'Penetrate the fortress defenses';
        this.backgroundColor = '#220000';
        this.duration = 70000;
        
        // Create fortress background
        this.createFortressBackground();
        
        this.enemyWaves = [
            { time: 0, enemies: [{ type: 'heavy', count: 2, interval: 1500 }, { type: 'shooter', count: 4, interval: 800 }] },
            { time: 10000, enemies: [{ type: 'formation', count: 12, interval: 200 }] },
            { time: 18000, enemies: [{ type: 'circular', count: 6, interval: 1000 }] },
            { time: 26000, enemies: [{ type: 'heavy', count: 4, interval: 1200 }] },
            { time: 34000, enemies: [{ type: 'zigzag', count: 8, interval: 600 }, { type: 'fast', count: 6, interval: 400 }] },
            { time: 42000, enemies: [{ type: 'shooter', count: 8, interval: 600 }] },
            { time: 50000, enemies: [{ type: 'heavy', count: 6, interval: 1000 }] },
            { time: 58000, enemies: [{ type: 'formation', count: 15, interval: 150 }] }
        ];
    }

    createAsteroidBackground() {
        // Create floating asteroids
        for (let i = 0; i < 20; i++) {
            this.backgroundLayers.push({
                type: 'asteroid',
                x: Utils.random(0, GAME_CONFIG.CANVAS_WIDTH * 2),
                y: Utils.random(0, GAME_CONFIG.CANVAS_HEIGHT),
                size: Utils.random(10, 40),
                speed: Utils.random(0.5, 2),
                rotation: Utils.random(0, Math.PI * 2),
                rotationSpeed: Utils.random(-0.02, 0.02)
            });
        }
    }

    createSpaceStationBackground() {
        // Create space station structures
        this.backgroundLayers.push({
            type: 'station',
            x: GAME_CONFIG.CANVAS_WIDTH * 0.8,
            y: GAME_CONFIG.CANVAS_HEIGHT * 0.3,
            width: 200,
            height: 300,
            speed: 1
        });
        
        // Add defense platforms
        for (let i = 0; i < 5; i++) {
            this.backgroundLayers.push({
                type: 'platform',
                x: Utils.random(GAME_CONFIG.CANVAS_WIDTH, GAME_CONFIG.CANVAS_WIDTH * 1.5),
                y: Utils.random(50, GAME_CONFIG.CANVAS_HEIGHT - 50),
                size: Utils.random(20, 35),
                speed: Utils.random(0.8, 1.5)
            });
        }
    }

    createNebulaBackground() {
        // Create nebula clouds with particle effects
        for (let i = 0; i < 15; i++) {
            this.backgroundLayers.push({
                type: 'nebula',
                x: Utils.random(0, GAME_CONFIG.CANVAS_WIDTH * 2),
                y: Utils.random(0, GAME_CONFIG.CANVAS_HEIGHT),
                size: Utils.random(80, 150),
                speed: Utils.random(0.3, 1),
                color: Utils.randomChoice(['#ff00ff', '#00ffff', '#ffff00']),
                alpha: Utils.random(0.1, 0.3)
            });
        }
    }

    createFleetBackground() {
        // Create large capital ships in the background
        for (let i = 0; i < 3; i++) {
            this.backgroundLayers.push({
                type: 'capitalShip',
                x: Utils.random(GAME_CONFIG.CANVAS_WIDTH, GAME_CONFIG.CANVAS_WIDTH * 2),
                y: Utils.random(100, GAME_CONFIG.CANVAS_HEIGHT - 100),
                width: Utils.random(150, 250),
                height: Utils.random(60, 100),
                speed: Utils.random(0.2, 0.8)
            });
        }
    }

    createFortressBackground() {
        // Create massive fortress structure
        this.backgroundLayers.push({
            type: 'fortress',
            x: GAME_CONFIG.CANVAS_WIDTH * 0.7,
            y: 0,
            width: 400,
            height: GAME_CONFIG.CANVAS_HEIGHT,
            speed: 0.5
        });
        
        // Add defensive turrets
        for (let i = 0; i < 8; i++) {
            this.backgroundLayers.push({
                type: 'turret',
                x: Utils.random(GAME_CONFIG.CANVAS_WIDTH * 0.8, GAME_CONFIG.CANVAS_WIDTH * 1.2),
                y: Utils.random(50, GAME_CONFIG.CANVAS_HEIGHT - 50),
                size: 15,
                speed: 0.5
            });
        }
    }

    update(deltaTime, gameEngine) {
        this.waveTimer += deltaTime;
        
        // Update background elements
        this.updateBackground(deltaTime);
        
        // Spawn enemy waves
        this.updateEnemyWaves(deltaTime, gameEngine);
        
        // Check for stage completion
        this.checkStageCompletion(gameEngine);
        
        // Update environmental effects
        this.updateEnvironmentalEffects(deltaTime);
    }

    updateBackground(deltaTime) {
        this.backgroundLayers.forEach(layer => {
            layer.x -= layer.speed * deltaTime * 0.1;
            
            if (layer.rotation !== undefined) {
                layer.rotation += layer.rotationSpeed * deltaTime * 0.1;
            }
            
            // Reset position when off screen
            if (layer.x + (layer.width || layer.size || 0) < 0) {
                layer.x = GAME_CONFIG.CANVAS_WIDTH + (layer.width || layer.size || 0);
                layer.y = Utils.random(0, GAME_CONFIG.CANVAS_HEIGHT);
            }
        });
    }

    updateEnemyWaves(deltaTime, gameEngine) {
        if (this.currentWave >= this.enemyWaves.length) return;
        
        const currentWaveData = this.enemyWaves[this.currentWave];
        
        if (this.waveTimer >= currentWaveData.time) {
            this.spawnWave(currentWaveData, gameEngine);
            this.currentWave++;
        }
    }

    spawnWave(waveData, gameEngine) {
        waveData.enemies.forEach(enemyGroup => {
            for (let i = 0; i < enemyGroup.count; i++) {
                setTimeout(() => {
                    const x = GAME_CONFIG.CANVAS_WIDTH + 50;
                    const y = Utils.random(50, GAME_CONFIG.CANVAS_HEIGHT - 50);
                    const enemy = new Enemy(x, y, enemyGroup.type);
                    
                    // Apply difficulty scaling
                    enemy.health = Math.ceil(enemy.health * this.difficultyMultiplier);
                    enemy.maxHealth = enemy.health;
                    enemy.speed *= (1 + (this.stageNumber - 1) * 0.1);
                    
                    gameEngine.enemies.push(enemy);
                }, i * enemyGroup.interval);
            }
        });
    }

    checkStageCompletion(gameEngine) {
        if (this.isComplete) return;
        
        const allWavesSpawned = this.currentWave >= this.enemyWaves.length;
        const noEnemiesLeft = gameEngine.enemies.length === 0;
        const bossDefeated = this.bossSpawned && gameEngine.bosses.length === 0;
        
        if (allWavesSpawned && noEnemiesLeft) {
            if (this.hasBoss && !this.bossSpawned) {
                this.spawnBoss(gameEngine);
            } else if (!this.hasBoss || bossDefeated) {
                this.isComplete = true;
            }
        }
    }

    spawnBoss(gameEngine) {
        this.bossSpawned = true;
        const boss = new Boss(this.stageNumber);
        gameEngine.bosses.push(boss);
        
        // Play boss music
        if (window.audioManager) {
            window.audioManager.playMusic('boss');
        }
    }

    updateEnvironmentalEffects(deltaTime) {
        // Stage-specific environmental effects
        switch (this.stageNumber) {
            case 3: // Nebula - electrical storms
                if (Math.random() < 0.001) {
                    this.createLightningEffect();
                }
                break;
            case 5: // Core Assault - energy discharges
                if (Math.random() < 0.002) {
                    this.createEnergyDischarge();
                }
                break;
        }
    }

    createLightningEffect() {
        if (!window.particleSystem) return;
        
        const startX = Utils.random(0, GAME_CONFIG.CANVAS_WIDTH);
        const startY = 0;
        const endX = startX + Utils.random(-100, 100);
        const endY = GAME_CONFIG.CANVAS_HEIGHT;
        
        // Create lightning particles
        for (let i = 0; i < 20; i++) {
            const t = i / 19;
            const x = Utils.lerp(startX, endX, t) + Utils.random(-10, 10);
            const y = Utils.lerp(startY, endY, t);
            
            const particle = new Particle(x, y, 0, 0, {
                life: 0.1,
                decay: 0.1,
                size: 2,
                color: '#ffffff',
                glow: true
            });
            
            window.particleSystem.addParticle(particle);
        }
    }

    createEnergyDischarge() {
        if (!window.particleSystem) return;
        
        const x = Utils.random(GAME_CONFIG.CANVAS_WIDTH * 0.8, GAME_CONFIG.CANVAS_WIDTH);
        const y = Utils.random(0, GAME_CONFIG.CANVAS_HEIGHT);
        
        window.particleSystem.createExplosion(x, y, {
            count: 15,
            colors: ['#ff0000', '#ffff00', '#ffffff'],
            speed: 3
        });
    }

    render(ctx) {
        // Render background
        ctx.fillStyle = this.backgroundColor;
        ctx.fillRect(0, 0, GAME_CONFIG.CANVAS_WIDTH, GAME_CONFIG.CANVAS_HEIGHT);
        
        // Render background layers
        this.renderBackgroundLayers(ctx);
    }

    renderBackgroundLayers(ctx) {
        this.backgroundLayers.forEach(layer => {
            ctx.save();
            
            switch (layer.type) {
                case 'asteroid':
                    this.renderAsteroid(ctx, layer);
                    break;
                case 'station':
                    this.renderSpaceStation(ctx, layer);
                    break;
                case 'platform':
                    this.renderPlatform(ctx, layer);
                    break;
                case 'nebula':
                    this.renderNebula(ctx, layer);
                    break;
                case 'capitalShip':
                    this.renderCapitalShip(ctx, layer);
                    break;
                case 'fortress':
                    this.renderFortress(ctx, layer);
                    break;
                case 'turret':
                    this.renderTurret(ctx, layer);
                    break;
            }
            
            ctx.restore();
        });
    }

    renderAsteroid(ctx, asteroid) {
        ctx.translate(asteroid.x, asteroid.y);
        ctx.rotate(asteroid.rotation);
        ctx.fillStyle = '#666666';
        ctx.fillRect(-asteroid.size / 2, -asteroid.size / 2, asteroid.size, asteroid.size);
        ctx.strokeStyle = '#888888';
        ctx.strokeRect(-asteroid.size / 2, -asteroid.size / 2, asteroid.size, asteroid.size);
    }

    renderSpaceStation(ctx, station) {
        ctx.fillStyle = '#444444';
        ctx.fillRect(station.x, station.y, station.width, station.height);
        ctx.fillStyle = '#666666';
        ctx.fillRect(station.x + 20, station.y + 20, station.width - 40, station.height - 40);
        
        // Add lights
        ctx.fillStyle = '#ffff00';
        for (let i = 0; i < 10; i++) {
            const x = station.x + Utils.random(0, station.width);
            const y = station.y + Utils.random(0, station.height);
            ctx.fillRect(x, y, 2, 2);
        }
    }

    renderPlatform(ctx, platform) {
        ctx.fillStyle = '#555555';
        ctx.fillRect(platform.x - platform.size / 2, platform.y - platform.size / 2, platform.size, platform.size);
        ctx.fillStyle = '#ff0000';
        ctx.fillRect(platform.x - 2, platform.y - 2, 4, 4);
    }

    renderNebula(ctx, nebula) {
        ctx.globalAlpha = nebula.alpha;
        ctx.fillStyle = nebula.color;
        ctx.fillRect(nebula.x - nebula.size / 2, nebula.y - nebula.size / 2, nebula.size, nebula.size);
        ctx.globalAlpha = 1;
    }

    renderCapitalShip(ctx, ship) {
        ctx.fillStyle = '#333333';
        ctx.fillRect(ship.x, ship.y, ship.width, ship.height);
        ctx.fillStyle = '#555555';
        ctx.fillRect(ship.x + 10, ship.y + 10, ship.width - 20, ship.height - 20);
        
        // Engine glow
        ctx.fillStyle = '#0088ff';
        ctx.fillRect(ship.x + ship.width - 10, ship.y + ship.height / 2 - 5, 10, 10);
    }

    renderFortress(ctx, fortress) {
        ctx.fillStyle = '#222222';
        ctx.fillRect(fortress.x, fortress.y, fortress.width, fortress.height);
        
        // Add structural details
        ctx.fillStyle = '#444444';
        for (let i = 0; i < fortress.height; i += 50) {
            ctx.fillRect(fortress.x, fortress.y + i, fortress.width, 10);
        }
    }

    renderTurret(ctx, turret) {
        ctx.fillStyle = '#666666';
        ctx.fillRect(turret.x - turret.size / 2, turret.y - turret.size / 2, turret.size, turret.size);
        ctx.fillStyle = '#ff0000';
        ctx.fillRect(turret.x - 3, turret.y - 3, 6, 6);
    }
}

// Stage Manager
class StageManager {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        this.currentStage = null;
        this.stageNumber = 1;
    }

    loadStage(stageNumber) {
        this.stageNumber = stageNumber;
        this.currentStage = new Stage(stageNumber);
        
        console.log(`Loading stage ${stageNumber}: ${this.currentStage.name}`);
        
        // Play appropriate music
        if (window.audioManager) {
            window.audioManager.playMusic('stage1');
        }
    }

    update(deltaTime) {
        if (this.currentStage) {
            this.currentStage.update(deltaTime, this.gameEngine);
        }
    }

    render(ctx) {
        if (this.currentStage) {
            this.currentStage.render(ctx);
        }
    }

    isStageComplete() {
        return this.currentStage && this.currentStage.isComplete;
    }

    getCurrentStageName() {
        return this.currentStage ? this.currentStage.name : '';
    }
}
